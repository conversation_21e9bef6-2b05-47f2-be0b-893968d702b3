"""
Mock AI analyzer module for testing without heavy ML dependencies.
"""
import logging
import re
from typing import List, Dict, Any, Optional
from config import Config

logger = logging.getLogger(__name__)

class AIAnalyzer:
    """Mock class for analyzing text using simple pattern matching."""
    
    def __init__(self, model_name: Optional[str] = None):
        """
        Initialize the mock AI analyzer.
        
        Args:
            model_name (str, optional): Model name (ignored in mock)
        """
        self.model_name = model_name or "mock-model"
        self.config = Config()
        logger.info(f"Using mock AI analyzer (model: {self.model_name})")
    
    def extract_entities(self, text: str) -> List[Dict[str, Any]]:
        """
        Mock entity extraction using simple pattern matching.
        
        Args:
            text (str): Input text to analyze
            
        Returns:
            List[Dict[str, Any]]: List of mock entities
        """
        entities = []
        
        # Simple patterns for common entities
        patterns = {
            'PERSON': r'\b[A-Z][a-z]+\s+[A-Z][a-z]+\b',
            'ORG': r'\b(?:Inc|Corp|LLC|Ltd|Company|Technologies|Solutions|Systems)\b',
            'SKILL': r'\b(?:Python|Java|JavaScript|React|Node\.js|SQL|AWS|Docker|Kubernetes)\b',
            'EMAIL': r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            'PHONE': r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b'
        }
        
        for entity_type, pattern in patterns.items():
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                entities.append({
                    'word': match.group(),
                    'entity_group': entity_type,
                    'score': 0.9,  # Mock confidence score
                    'start': match.start(),
                    'end': match.end()
                })
        
        return entities
    
    def extract_skills_from_entities(self, entities: List[Dict[str, Any]], text: str) -> List[Dict[str, Any]]:
        """
        Extract skills from entities and match with predefined skills.
        
        Args:
            entities (List[Dict[str, Any]]): Entities from extract_entities
            text (str): Original text for context
            
        Returns:
            List[Dict[str, Any]]: Extracted skills with metadata
        """
        skills = []
        predefined_skills = self.config.get_all_skills()
        text_lower = text.lower()
        
        # Extract from entities
        for entity in entities:
            if entity.get('entity_group') == 'SKILL':
                entity_text = entity['word'].lower()
                
                # Check if entity matches predefined skills
                for skill in predefined_skills:
                    if skill.lower() in entity_text or entity_text in skill.lower():
                        skills.append({
                            'skill': skill,
                            'confidence': float(entity['score']),
                            'source': 'mock_entity',
                            'context': entity.get('word', '')
                        })
        
        # Enhanced pattern matching for predefined skills
        for skill in predefined_skills:
            # Multiple pattern variations for better matching
            patterns = [
                r'\b' + re.escape(skill.lower()) + r'\b',  # Exact match
                r'\b' + re.escape(skill.lower()) + r's?\b',  # Plural form
            ]

            # Special patterns for common variations
            if skill.lower() == 'javascript':
                patterns.append(r'\bjs\b')
            elif skill.lower() == 'typescript':
                patterns.append(r'\bts\b')
            elif skill.lower() == 'postgresql':
                patterns.extend([r'\bpostgres\b', r'\bpsql\b'])
            elif skill.lower() == 'mongodb':
                patterns.append(r'\bmongo\b')
            elif skill.lower() == 'kubernetes':
                patterns.append(r'\bk8s\b')

            skill_found = False
            for pattern in patterns:
                matches = list(re.finditer(pattern, text_lower))

                if matches and not skill_found:
                    # Extract surrounding context from first match
                    match = matches[0]
                    start = max(0, match.start() - 50)
                    end = min(len(text), match.end() + 50)
                    context = text[start:end].strip()

                    # Higher confidence for exact matches
                    confidence = 0.9 if pattern == patterns[0] else 0.8

                    skills.append({
                        'skill': skill,
                        'confidence': confidence,
                        'source': 'pattern_match',
                        'context': context
                    })
                    skill_found = True
                    break
        
        # Remove duplicates and sort by confidence
        unique_skills = {}
        for skill_info in skills:
            skill_name = skill_info['skill'].lower()
            if skill_name not in unique_skills or skill_info['confidence'] > unique_skills[skill_name]['confidence']:
                unique_skills[skill_name] = skill_info

        # Filter out false positives
        filtered_skills = self._filter_false_positives(list(unique_skills.values()))

        return sorted(filtered_skills, key=lambda x: x['confidence'], reverse=True)
    
    def extract_experience_years(self, text: str, skills: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Extract years of experience for identified skills.
        
        Args:
            text (str): Text to analyze
            skills (List[Dict[str, Any]]): Identified skills
            
        Returns:
            Dict[str, Any]: Skills with experience years
        """
        experience_data = {}
        
        # Simple patterns to match experience mentions
        year_patterns = [
            r'(\d+)\s*(?:years?|yrs?)\s*(?:of\s*)?(?:experience\s*)?(?:with\s*|in\s*|using\s*|of\s*)?([^.\n]+)',
            r'([^.\n]+)\s*(?:for\s*|:\s*|\(\s*)(\d+)\s*(?:years?|yrs?)',
            r'(\d+)\+?\s*(?:years?|yrs?)\s*(?:of\s*|with\s*|in\s*|using\s*)?([^.\n]+)',
        ]
        
        text_lower = text.lower()
        
        for skill_info in skills:
            skill = skill_info['skill'].lower()
            years = None

            # Create skill variations for better matching
            skill_variations = [skill]
            if skill == 'javascript':
                skill_variations.extend(['js', 'java script'])
            elif skill == 'typescript':
                skill_variations.extend(['ts', 'type script'])
            elif skill == 'postgresql':
                skill_variations.extend(['postgres', 'psql'])
            elif skill == 'mongodb':
                skill_variations.extend(['mongo'])
            elif skill == 'kubernetes':
                skill_variations.extend(['k8s'])

            # Look for experience mentions near the skill
            for pattern in year_patterns:
                matches = re.finditer(pattern, text_lower, re.IGNORECASE | re.MULTILINE)

                for match in matches:
                    match_text = match.group(0).lower()
                    groups = match.groups()

                    # Check if any skill variation is mentioned in the match
                    skill_found = any(var in match_text for var in skill_variations)

                    if skill_found:
                        # Extract the number - it could be in different groups
                        year_candidates = []
                        for group in groups:
                            if group and re.search(r'\d+', group):
                                numbers = re.findall(r'\d+', group)
                                year_candidates.extend([int(n) for n in numbers if 0 < int(n) <= 50])

                        if year_candidates:
                            # Take the most reasonable year value
                            years = min(year_candidates)  # Avoid unrealistic high numbers
                            break

                if years:
                    break
            
            experience_data[skill_info['skill']] = {
                'years': years,
                'confidence': skill_info['confidence'],
                'context': skill_info.get('context', ''),
                'source': skill_info.get('source', '')
            }
        
        return experience_data

    def _filter_false_positives(self, skills: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Filter out obvious false positive skills.

        Args:
            skills (List[Dict[str, Any]]): Skills list

        Returns:
            List[Dict[str, Any]]: Filtered skills
        """
        # List of skills that are often false positives in job descriptions
        false_positive_skills = {
            'v',  # Often matches random 'v' characters
            'chai',  # Often matches random text containing 'chai'
            'gin',  # Often matches random text containing 'gin'
            'lit',  # Often matches random text containing 'lit'
            'pip',  # Often matches random text containing 'pip'
            'lean',  # Often matches random text containing 'lean'
            'unity',  # Often matches random text containing 'unity'
            'defi',  # Often matches random text containing 'defi'
            'scala',  # Often matches random text containing 'scala'
            't-sql',  # Often matches when not specifically mentioned
            'plsql',  # Often matches when not specifically mentioned
        }

        filtered_skills = []
        for skill_info in skills:
            skill_name = skill_info['skill'].lower()

            # Skip obvious false positives
            if skill_name in false_positive_skills:
                continue

            # For single letter skills, require very high confidence and context validation
            if len(skill_name) == 1:
                if skill_info['confidence'] < 0.95:
                    continue
                # Additional validation: check if it appears as a standalone programming language
                context = skill_info.get('context', '').lower()
                if not any(indicator in context for indicator in ['programming', 'language', 'code', 'develop']):
                    continue

            # For very short skills (2 chars), require high confidence
            if len(skill_name) == 2 and skill_info['confidence'] < 0.9:
                continue

            filtered_skills.append(skill_info)

        return filtered_skills
