"""
Main job description screener class that orchestrates the entire job analysis process.
"""
import logging
import json
import re
from typing import Dict, Any, Optional, List
from datetime import datetime

from .pdf_reader import PDFReader
from .ai_analyzer_mock import AIAnalyzer
from .job_requirements_extractor import JobRequirementsExtractor
from config import Config

logger = logging.getLogger(__name__)

class JobDescriptionScreener:
    """Main class for screening job descriptions and extracting requirements."""
    
    def __init__(self, model_name: Optional[str] = None):
        """
        Initialize the job description screener.
        
        Args:
            model_name (str, optional): Name of the AI model to use
        """
        self.config = Config()
        self.pdf_reader = PDFReader()
        self.ai_analyzer = AIAnalyzer(model_name)
        self.requirements_extractor = JobRequirementsExtractor()
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    def process_job_description(self, file_path: str) -> Dict[str, Any]:
        """
        Process a job description file and extract requirements.
        
        Args:
            file_path (str): Path to the job description file
            
        Returns:
            Dict[str, Any]: Complete analysis results
        """
        logger.info(f"Starting job description analysis for: {file_path}")
        
        result = {
            'file_path': file_path,
            'timestamp': datetime.now().isoformat(),
            'success': False,
            'error': None,
            'extraction_method': None,
            'raw_text': None,
            'entities': [],
            'job_info': {},
            'requirements': {},
            'summary': {}
        }
        
        try:
            # Step 1: Extract text from document
            logger.info("Extracting text from document...")
            pdf_result = self.pdf_reader.extract_text(file_path)
            
            if not pdf_result['success']:
                result['error'] = f"Text extraction failed: {pdf_result.get('error', 'Unknown error')}"
                return result
            
            result['raw_text'] = pdf_result['text']
            result['extraction_method'] = pdf_result['method_used']
            
            # Step 2: Analyze text with AI model
            logger.info("Analyzing text with AI model...")
            entities = self.ai_analyzer.extract_entities(pdf_result['text'])
            result['entities'] = entities
            
            # Step 3: Extract job information
            logger.info("Extracting job information...")
            job_info = self._extract_job_info(pdf_result['text'])
            result['job_info'] = job_info
            
            # Step 4: Extract detailed requirements
            logger.info("Extracting detailed requirements...")
            requirements = self.requirements_extractor.extract_requirements(pdf_result['text'])
            result['requirements'] = requirements
            
            # Step 5: Extract skills with AI
            logger.info("Extracting skills with AI...")
            skills = self.ai_analyzer.extract_skills_from_entities(entities, pdf_result['text'])
            
            # Categorize AI-extracted skills by requirement level
            categorized_skills = self._categorize_ai_skills(skills, pdf_result['text'])
            result['requirements']['ai_skills'] = categorized_skills
            
            # Step 6: Generate summary
            logger.info("Generating summary...")
            summary = self._generate_summary(job_info, requirements, categorized_skills)
            result['summary'] = summary
            
            result['success'] = True
            logger.info("Job description analysis completed successfully")
            
        except Exception as e:
            logger.error(f"Error processing job description: {str(e)}")
            result['error'] = str(e)
        
        return result
    
    def process_multiple_job_descriptions(self, file_paths: List[str]) -> Dict[str, Any]:
        """
        Process multiple job description files.
        
        Args:
            file_paths (List[str]): List of file paths
            
        Returns:
            Dict[str, Any]: Results for all files
        """
        results = {
            'total_files': len(file_paths),
            'successful': 0,
            'failed': 0,
            'results': []
        }
        
        for file_path in file_paths:
            try:
                result = self.process_job_description(file_path)
                results['results'].append(result)
                
                if result['success']:
                    results['successful'] += 1
                else:
                    results['failed'] += 1
                    
            except Exception as e:
                logger.error(f"Error processing {file_path}: {str(e)}")
                results['results'].append({
                    'file_path': file_path,
                    'success': False,
                    'error': str(e)
                })
                results['failed'] += 1
        
        return results
    
    def _extract_job_info(self, text: str) -> Dict[str, Any]:
        """
        Extract basic job information from job description.
        
        Args:
            text (str): Job description text
            
        Returns:
            Dict[str, Any]: Job information
        """
        job_info = {
            'title': None,
            'company': None,
            'location': None,
            'employment_type': None,
            'salary_range': None,
            'responsibilities': [],
            'benefits': []
        }
        
        # Extract job title (usually in first few lines)
        lines = text.split('\n')[:5]
        for line in lines:
            line = line.strip()
            if len(line) > 5 and len(line) < 100 and not line.lower().startswith(('company', 'location')):
                job_info['title'] = line
                break
        
        # Extract company name
        company_patterns = [
            r'company:\s*([^\n]+)',
            r'(?:at|join)\s+([A-Z][a-zA-Z\s&]+?)(?:\s+we|\s+is|\s+,)',
        ]
        
        for pattern in company_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                job_info['company'] = match.group(1).strip()
                break
        
        # Extract location
        location_patterns = [
            r'location:\s*([^\n]+)',
            r'([A-Z][a-z]+,\s*[A-Z]{2})',  # City, State
            r'(?:remote|work from home|wfh)',
        ]
        
        for pattern in location_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                location = match.group(1) if match.lastindex else match.group(0)
                job_info['location'] = location.strip()
                break
        
        # Extract employment type
        text_lower = text.lower()
        if 'full time' in text_lower or 'full-time' in text_lower:
            job_info['employment_type'] = 'full-time'
        elif 'part time' in text_lower or 'part-time' in text_lower:
            job_info['employment_type'] = 'part-time'
        elif 'contract' in text_lower:
            job_info['employment_type'] = 'contract'
        elif 'intern' in text_lower:
            job_info['employment_type'] = 'internship'
        
        # Extract salary range
        salary_patterns = [
            r'\$(\d{1,3}(?:,\d{3})*)\s*-\s*\$(\d{1,3}(?:,\d{3})*)',
            r'(\d{1,3}(?:,\d{3})*)\s*-\s*(\d{1,3}(?:,\d{3})*)\s*(?:per year|annually)',
        ]
        
        for pattern in salary_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                min_salary = int(match.group(1).replace(',', ''))
                max_salary = int(match.group(2).replace(',', ''))
                job_info['salary_range'] = {
                    'min': min_salary,
                    'max': max_salary,
                    'currency': 'USD'
                }
                break
        
        # Extract responsibilities (simple extraction)
        resp_section = re.search(r'(?:responsibilities|duties):\s*(.*?)(?:\n\n|\n[A-Z]|$)', text, re.IGNORECASE | re.DOTALL)
        if resp_section:
            resp_text = resp_section.group(1)
            responsibilities = re.split(r'[•\-\*]\s*|(?:\n\s*)+', resp_text)
            job_info['responsibilities'] = [r.strip() for r in responsibilities if len(r.strip()) > 10][:5]
        
        # Extract benefits
        benefit_keywords = [
            'health insurance', 'dental', 'vision', '401k', 'retirement',
            'vacation', 'pto', 'flexible hours', 'remote work', 'stock options'
        ]
        
        for keyword in benefit_keywords:
            if keyword in text.lower():
                job_info['benefits'].append(keyword)
        
        return job_info
    
    def _categorize_ai_skills(self, skills: List[Dict[str, Any]], text: str) -> Dict[str, List[Dict[str, Any]]]:
        """
        Categorize AI-extracted skills by requirement level.
        
        Args:
            skills (List[Dict[str, Any]]): AI-extracted skills
            text (str): Job description text
            
        Returns:
            Dict[str, List[Dict[str, Any]]]: Skills categorized by requirement level
        """
        categorized = {'required': [], 'preferred': []}
        text_lower = text.lower()
        
        for skill_info in skills:
            skill_name = skill_info['skill'].lower()
            
            # Find skill mentions and analyze context
            skill_positions = [m.start() for m in re.finditer(re.escape(skill_name), text_lower)]
            
            is_required = False
            for pos in skill_positions:
                # Check context around skill mention
                start = max(0, pos - 100)
                end = min(len(text_lower), pos + len(skill_name) + 100)
                context = text_lower[start:end]
                
                # Check for required indicators
                required_indicators = self.requirements_extractor.required_indicators
                if any(indicator in context for indicator in required_indicators):
                    is_required = True
                    break
            
            if is_required:
                categorized['required'].append(skill_info)
            else:
                categorized['preferred'].append(skill_info)
        
        return categorized
    
    def _generate_summary(self, job_info: Dict[str, Any], requirements: Dict[str, Any], ai_skills: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate summary of job description analysis.
        
        Args:
            job_info (Dict[str, Any]): Job information
            requirements (Dict[str, Any]): Requirements data
            ai_skills (Dict[str, Any]): AI-extracted skills
            
        Returns:
            Dict[str, Any]: Summary
        """
        summary = {
            'job_title': job_info.get('title'),
            'company_name': job_info.get('company'),
            'job_level': requirements.get('job_level'),
            'location': job_info.get('location'),
            'employment_type': job_info.get('employment_type'),
            
            # Requirements summary from requirements extractor
            'total_required_skills': requirements.get('summary', {}).get('total_required_skills', 0),
            'total_preferred_skills': requirements.get('summary', {}).get('total_preferred_skills', 0),
            'has_experience_requirements': requirements.get('summary', {}).get('has_experience_requirements', False),
            'has_education_requirements': requirements.get('summary', {}).get('has_education_requirements', False),
            'has_certifications': requirements.get('summary', {}).get('has_certifications', False),
            'soft_skills_count': requirements.get('summary', {}).get('soft_skills_count', 0),
            
            # Top skills from both extractors
            'top_required_skills': requirements.get('summary', {}).get('top_required_skills', []),
            'top_preferred_skills': requirements.get('summary', {}).get('top_preferred_skills', []),
            'ai_required_skills': [skill['skill'] for skill in ai_skills.get('required', [])[:5]],
            'ai_preferred_skills': [skill['skill'] for skill in ai_skills.get('preferred', [])[:5]],
            
            # Experience and education
            'experience_years': requirements.get('experience_requirements', {}).get('required_years'),
            'education_requirements': [req['requirement'] for req in requirements.get('education_requirements', [])[:3]],
            'certifications': [cert['certification'] for cert in requirements.get('certifications', [])[:3]],
            
            # Job details
            'salary_range': job_info.get('salary_range'),
            'benefits_count': len(job_info.get('benefits', [])),
            'responsibilities_count': len(job_info.get('responsibilities', []))
        }
        
        return summary
    
    def save_results(self, results: Dict[str, Any], output_path: str, format_type: str = 'json') -> bool:
        """
        Save analysis results to file.
        
        Args:
            results (Dict[str, Any]): Analysis results
            output_path (str): Output file path
            format_type (str): Output format ('json' or 'csv')
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if format_type == 'json':
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(results, f, indent=2, ensure_ascii=False)
            else:
                logger.error(f"Unsupported format: {format_type}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error saving results: {str(e)}")
            return False
