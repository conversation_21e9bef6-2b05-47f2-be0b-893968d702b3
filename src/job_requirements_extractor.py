"""
Job requirements extractor module for analyzing job descriptions.
Extracts required skills, experience, education, and nice-to-have qualifications.
"""
import logging
import re
from typing import Dict, List, Any
from collections import defaultdict
from config import Config

logger = logging.getLogger(__name__)

class JobRequirementsExtractor:
    """Class for extracting requirements from job descriptions."""
    
    def __init__(self):
        """Initialize the job requirements extractor."""
        self.config = Config()
        
        # Job description keywords for requirement detection
        self.required_indicators = [
            'required', 'must have', 'essential', 'mandatory', 'necessary',
            'minimum', 'minimum requirements', 'qualifications required',
            'you must', 'you will need', 'candidate must', 'should have'
        ]
        
        self.preferred_indicators = [
            'preferred', 'nice to have', 'bonus', 'plus', 'advantage',
            'desirable', 'would be great', 'ideal candidate', 'we would love',
            'additional', 'beneficial', 'a plus', 'good to have'
        ]
    
    def extract_requirements(self, text: str) -> Dict[str, Any]:
        """
        Extract all requirements from job description text.
        
        Args:
            text (str): Job description text
            
        Returns:
            Dict[str, Any]: Extracted requirements categorized by type
        """
        requirements = {
            'required_skills': [],
            'preferred_skills': [],
            'experience_requirements': {},
            'education_requirements': [],
            'certifications': [],
            'soft_skills': [],
            'job_level': None,
            'summary': {}
        }
        
        # Extract skills with requirement level
        skills_data = self._extract_skills_with_priority(text)
        requirements['required_skills'] = skills_data['required']
        requirements['preferred_skills'] = skills_data['preferred']
        
        # Extract experience requirements
        requirements['experience_requirements'] = self._extract_experience_requirements(text)
        
        # Extract education requirements
        requirements['education_requirements'] = self._extract_education_requirements(text)
        
        # Extract certifications
        requirements['certifications'] = self._extract_certifications(text)
        
        # Extract soft skills
        requirements['soft_skills'] = self._extract_soft_skills(text)
        
        # Determine job level
        requirements['job_level'] = self._determine_job_level(text)
        
        # Generate summary
        requirements['summary'] = self._generate_requirements_summary(requirements)
        
        return requirements
    
    def _extract_skills_with_priority(self, text: str) -> Dict[str, List[Dict[str, Any]]]:
        """
        Extract technical skills and categorize by priority (required vs preferred).
        
        Args:
            text (str): Job description text
            
        Returns:
            Dict[str, List[Dict[str, Any]]]: Skills categorized by priority
        """
        skills_data = {'required': [], 'preferred': []}
        predefined_skills = self.config.get_all_skills()
        text_lower = text.lower()
        
        # Split text into sections for better context analysis
        sections = self._split_into_sections(text)
        
        for skill in predefined_skills:
            skill_lower = skill.lower()

            # Skip very short skills that are likely false positives
            # Only allow well-known short programming languages and technologies
            if len(skill_lower) <= 2:
                allowed_short_skills = ['r', 'go', 'c', 'js']  # Conservative list
                if skill_lower not in allowed_short_skills:
                    continue

            # Create skill variations for better matching
            skill_variations = self._get_skill_variations(skill_lower)
            
            for section_name, section_text in sections.items():
                section_lower = section_text.lower()
                
                # Check if skill is mentioned in this section
                skill_found = any(var in section_lower for var in skill_variations)
                
                if skill_found:
                    # Determine if it's required or preferred based on context
                    priority = self._determine_skill_priority(section_text, skill_variations)
                    
                    # Extract context around the skill mention
                    context = self._extract_skill_context(section_text, skill_variations)
                    
                    skill_info = {
                        'skill': skill,
                        'context': context,
                        'section': section_name,
                        'confidence': self._calculate_skill_confidence(context, skill_variations)
                    }
                    
                    if priority == 'required':
                        skills_data['required'].append(skill_info)
                    else:
                        skills_data['preferred'].append(skill_info)
                    
                    break  # Avoid duplicates across sections
        
        # Remove duplicates and sort by confidence
        skills_data['required'] = self._deduplicate_skills(skills_data['required'])
        skills_data['preferred'] = self._deduplicate_skills(skills_data['preferred'])

        # Filter out obvious false positives
        skills_data['required'] = self._filter_false_positives(skills_data['required'])
        skills_data['preferred'] = self._filter_false_positives(skills_data['preferred'])

        return skills_data
    
    def _split_into_sections(self, text: str) -> Dict[str, str]:
        """
        Split job description into logical sections.
        
        Args:
            text (str): Job description text
            
        Returns:
            Dict[str, str]: Text sections by name
        """
        sections = {'full_text': text}
        
        # Common section headers
        section_patterns = {
            'requirements': r'(?:requirements?|qualifications?|what (?:you|we) need|must have)',
            'responsibilities': r'(?:responsibilities|duties|what (?:you|we) (?:will|\'ll) do|role)',
            'preferred': r'(?:preferred|nice to have|bonus|plus|ideal|would be great)',
            'experience': r'(?:experience|background|years)',
            'education': r'(?:education|degree|university|college)',
            'skills': r'(?:skills|technical|technologies|tools)'
        }
        
        for section_name, pattern in section_patterns.items():
            # Find section headers and extract content
            matches = list(re.finditer(f'({pattern})', text, re.IGNORECASE))
            
            for match in matches:
                start = match.start()
                # Find the end of this section (next header or end of text)
                end = len(text)
                
                # Look for next section header
                for other_pattern in section_patterns.values():
                    if other_pattern != pattern:
                        next_matches = list(re.finditer(f'({other_pattern})', text[start + 100:], re.IGNORECASE))
                        if next_matches:
                            end = min(end, start + 100 + next_matches[0].start())
                
                section_text = text[start:end].strip()
                if len(section_text) > 50:  # Only include substantial sections
                    sections[section_name] = section_text
                    break
        
        return sections
    
    def _get_skill_variations(self, skill: str) -> List[str]:
        """
        Get variations of a skill name for better matching.
        
        Args:
            skill (str): Base skill name
            
        Returns:
            List[str]: Skill variations
        """
        variations = [skill]
        
        # Add common variations
        if skill == 'javascript':
            variations.extend(['js', 'java script', 'ecmascript'])
        elif skill == 'typescript':
            variations.extend(['ts', 'type script'])
        elif skill == 'postgresql':
            variations.extend(['postgres', 'psql', 'pg'])
        elif skill == 'mongodb':
            variations.extend(['mongo', 'mongo db'])
        elif skill == 'kubernetes':
            variations.extend(['k8s', 'k8'])
        elif skill == 'docker':
            variations.extend(['containerization', 'containers'])
        elif skill == 'amazon web services':
            variations.extend(['aws', 'amazon aws'])
        elif skill == 'google cloud platform':
            variations.extend(['gcp', 'google cloud', 'gcloud'])
        elif skill == 'microsoft azure':
            variations.extend(['azure', 'ms azure'])
        
        return variations
    
    def _determine_skill_priority(self, section_text: str, skill_variations: List[str]) -> str:
        """
        Determine if a skill is required or preferred based on context.
        
        Args:
            section_text (str): Text section containing the skill
            skill_variations (List[str]): Skill name variations
            
        Returns:
            str: 'required' or 'preferred'
        """
        section_lower = section_text.lower()
        
        # Find skill mentions and check surrounding context
        for skill_var in skill_variations:
            # Use word boundaries to avoid false positives for short skills
            if len(skill_var) <= 2:
                # For very short skills, require word boundaries
                pattern = r'\b' + re.escape(skill_var) + r'\b'
            else:
                # For longer skills, use simple escape
                pattern = re.escape(skill_var)

            skill_positions = [m.start() for m in re.finditer(pattern, section_lower)]
            
            for pos in skill_positions:
                # Check context around skill mention (±100 characters)
                start = max(0, pos - 100)
                end = min(len(section_lower), pos + len(skill_var) + 100)
                context = section_lower[start:end]
                
                # Check for required indicators first (higher priority)
                for indicator in self.required_indicators:
                    if indicator in context:
                        return 'required'
                
                # Check for preferred indicators
                for indicator in self.preferred_indicators:
                    if indicator in context:
                        return 'preferred'
        
        # Default to required if no clear indicators
        return 'required'
    
    def _extract_skill_context(self, section_text: str, skill_variations: List[str]) -> str:
        """
        Extract context around skill mentions.
        
        Args:
            section_text (str): Text section
            skill_variations (List[str]): Skill variations
            
        Returns:
            str: Context around skill mention
        """
        for skill_var in skill_variations:
            # Use word boundaries for short skills to avoid false positives
            if len(skill_var) <= 2:
                pattern = r'\b' + re.escape(skill_var) + r'\b'
            else:
                pattern = re.escape(skill_var)
            match = re.search(pattern, section_text, re.IGNORECASE)
            
            if match:
                start = max(0, match.start() - 50)
                end = min(len(section_text), match.end() + 50)
                return section_text[start:end].strip()
        
        return ""
    
    def _calculate_skill_confidence(self, context: str, skill_variations: List[str]) -> float:
        """
        Calculate confidence score for skill extraction.
        
        Args:
            context (str): Context around skill mention
            skill_variations (List[str]): Skill variations
            
        Returns:
            float: Confidence score (0.0 to 1.0)
        """
        base_confidence = 0.7
        
        # Increase confidence for exact matches
        context_lower = context.lower()
        for skill_var in skill_variations:
            if skill_var in context_lower:
                # Check if it's a whole word match
                if re.search(r'\b' + re.escape(skill_var) + r'\b', context_lower):
                    base_confidence = min(1.0, base_confidence + 0.2)
                else:
                    base_confidence = min(1.0, base_confidence + 0.1)
        
        return base_confidence
    
    def _deduplicate_skills(self, skills: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Remove duplicate skills and keep the one with highest confidence.
        
        Args:
            skills (List[Dict[str, Any]]): Skills list
            
        Returns:
            List[Dict[str, Any]]: Deduplicated skills
        """
        unique_skills = {}
        
        for skill_info in skills:
            skill_name = skill_info['skill'].lower()
            if skill_name not in unique_skills or skill_info['confidence'] > unique_skills[skill_name]['confidence']:
                unique_skills[skill_name] = skill_info
        
        return sorted(unique_skills.values(), key=lambda x: x['confidence'], reverse=True)

    def _filter_false_positives(self, skills: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Filter out obvious false positive skills.

        Args:
            skills (List[Dict[str, Any]]): Skills list

        Returns:
            List[Dict[str, Any]]: Filtered skills
        """
        # List of skills that are often false positives in job descriptions
        false_positive_skills = {
            'v',  # Often matches random 'v' characters
            'chai',  # Often matches random text containing 'chai'
            'gin',  # Often matches random text containing 'gin'
            'lit',  # Often matches random text containing 'lit'
            'pip',  # Often matches random text containing 'pip'
            'lean',  # Often matches random text containing 'lean'
            'unity',  # Often matches random text containing 'unity'
            'defi',  # Often matches random text containing 'defi'
            'scala',  # Often matches random text containing 'scala'
            't-sql',  # Often matches when not specifically mentioned
            'plsql',  # Often matches when not specifically mentioned
        }

        filtered_skills = []
        for skill_info in skills:
            skill_name = skill_info['skill'].lower()

            # Skip obvious false positives
            if skill_name in false_positive_skills:
                continue

            # For single letter skills, require very high confidence and context validation
            if len(skill_name) == 1:
                if skill_info['confidence'] < 0.95:
                    continue
                # Additional validation: check if it appears as a standalone programming language
                context = skill_info.get('context', '').lower()
                if not any(indicator in context for indicator in ['programming', 'language', 'code', 'develop']):
                    continue

            # For very short skills (2 chars), require high confidence
            if len(skill_name) == 2 and skill_info['confidence'] < 0.9:
                continue

            filtered_skills.append(skill_info)

        return filtered_skills

    def _extract_experience_requirements(self, text: str) -> Dict[str, Any]:
        """
        Extract experience requirements from job description.

        Args:
            text (str): Job description text

        Returns:
            Dict[str, Any]: Experience requirements
        """
        experience_data = {'required_years': None, 'preferred_years': None, 'domains': []}
        text_lower = text.lower()

        # Patterns for experience extraction
        experience_patterns = [
            r'(\d+)\+?\s*(?:years?|yrs?)\s*(?:of\s*)?(?:experience|exp)',
            r'(?:minimum|at least|minimum of)\s*(\d+)\s*(?:years?|yrs?)',
            r'(\d+)\s*to\s*(\d+)\s*(?:years?|yrs?)',
            r'(?:experience|exp)\s*(?:of\s*)?(\d+)\+?\s*(?:years?|yrs?)'
        ]

        for pattern in experience_patterns:
            matches = re.finditer(pattern, text_lower)

            for match in matches:
                # Extract years
                groups = match.groups()
                years = None

                # Find the numeric value
                for group in groups:
                    if group and re.search(r'\d+', group):
                        numbers = re.findall(r'\d+', group)
                        if numbers:
                            years = int(numbers[0])
                            break

                if years:
                    # Determine if required or preferred based on context
                    context_start = max(0, match.start() - 100)
                    context_end = min(len(text), match.end() + 100)
                    context = text[context_start:context_end].lower()

                    is_required = any(indicator in context for indicator in self.required_indicators)

                    if is_required or not experience_data['required_years']:
                        experience_data['required_years'] = years
                    else:
                        experience_data['preferred_years'] = years

                    break

        return experience_data

    def _extract_education_requirements(self, text: str) -> List[Dict[str, Any]]:
        """
        Extract education requirements from job description.

        Args:
            text (str): Job description text

        Returns:
            List[Dict[str, Any]]: Education requirements
        """
        education_requirements = []
        text_lower = text.lower()

        # Education patterns
        education_patterns = [
            r'(?:bachelor|master|phd|doctorate)(?:\'s)?\s*(?:degree)?\s*(?:in\s*)?([^.\n]+)',
            r'(b\.?s\.?|m\.?s\.?|ph\.?d\.?|b\.?a\.?|m\.?a\.?)\s*(?:in\s*)?([^.\n]+)',
            r'(?:degree|education)\s*(?:in\s*)?([^.\n]+)'
        ]

        for pattern in education_patterns:
            matches = re.finditer(pattern, text_lower)

            for match in matches:
                education_text = match.group(0).strip()

                # Determine if required or preferred
                context_start = max(0, match.start() - 100)
                context_end = min(len(text), match.end() + 100)
                context = text[context_start:context_end].lower()

                is_required = any(indicator in context for indicator in self.required_indicators)
                priority = 'required' if is_required else 'preferred'

                # Extract field of study if available
                field = 'any field'
                if match.groups():
                    for group in match.groups():
                        if group and len(group.strip()) > 2:
                            field = group.strip()
                            break

                education_info = {
                    'requirement': education_text,
                    'field': field,
                    'priority': priority
                }

                education_requirements.append(education_info)

        return education_requirements

    def _extract_certifications(self, text: str) -> List[Dict[str, Any]]:
        """
        Extract certification requirements from job description.

        Args:
            text (str): Job description text

        Returns:
            List[Dict[str, Any]]: Certification requirements
        """
        certifications = []
        text_lower = text.lower()

        # Common certification patterns
        cert_patterns = [
            r'(?:aws|amazon)\s*(?:certified\s*)?([^.\n]+)',
            r'(?:azure|microsoft)\s*(?:certified\s*)?([^.\n]+)',
            r'(?:google cloud|gcp)\s*(?:certified\s*)?([^.\n]+)',
            r'(?:certified|certification)\s*(?:in\s*)?([^.\n]+)'
        ]

        for pattern in cert_patterns:
            matches = re.finditer(pattern, text_lower)

            for match in matches:
                cert_text = match.group(0).strip()

                # Determine priority
                context_start = max(0, match.start() - 100)
                context_end = min(len(text), match.end() + 100)
                context = text[context_start:context_end].lower()

                is_required = any(indicator in context for indicator in self.required_indicators)
                priority = 'required' if is_required else 'preferred'

                cert_info = {
                    'certification': cert_text,
                    'priority': priority
                }

                certifications.append(cert_info)

        return certifications

    def _extract_soft_skills(self, text: str) -> List[Dict[str, Any]]:
        """
        Extract soft skills from job description.

        Args:
            text (str): Job description text

        Returns:
            List[Dict[str, Any]]: Soft skills
        """
        soft_skills = []
        text_lower = text.lower()

        # Common soft skills
        soft_skill_keywords = [
            'communication', 'leadership', 'teamwork', 'problem solving',
            'analytical', 'creative', 'detail oriented', 'organized',
            'time management', 'adaptable', 'flexible', 'collaborative',
            'self motivated', 'independent', 'proactive', 'initiative'
        ]

        for skill in soft_skill_keywords:
            if skill in text_lower:
                # Find context
                pattern = re.escape(skill)
                match = re.search(pattern, text_lower)

                if match:
                    context_start = max(0, match.start() - 50)
                    context_end = min(len(text), match.end() + 50)
                    context = text[context_start:context_end]

                    soft_skill_info = {
                        'skill': skill,
                        'context': context
                    }

                    soft_skills.append(soft_skill_info)

        return soft_skills

    def _determine_job_level(self, text: str) -> str:
        """
        Determine job level/seniority from job description.

        Args:
            text (str): Job description text

        Returns:
            str: Job level
        """
        text_lower = text.lower()

        # Job level indicators
        if any(word in text_lower for word in ['senior', 'lead', 'principal', '5+ years', '7+ years']):
            return 'senior'
        elif any(word in text_lower for word in ['junior', 'entry level', 'graduate', 'intern']):
            return 'entry'
        elif any(word in text_lower for word in ['director', 'manager', 'head of', 'vp']):
            return 'executive'
        else:
            return 'mid'

    def _generate_requirements_summary(self, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate summary of extracted requirements.

        Args:
            requirements (Dict[str, Any]): Extracted requirements

        Returns:
            Dict[str, Any]: Requirements summary
        """
        summary = {
            'total_required_skills': len(requirements['required_skills']),
            'total_preferred_skills': len(requirements['preferred_skills']),
            'has_experience_requirements': bool(requirements['experience_requirements'].get('required_years')),
            'has_education_requirements': len(requirements['education_requirements']) > 0,
            'has_certifications': len(requirements['certifications']) > 0,
            'soft_skills_count': len(requirements['soft_skills']),
            'job_level': requirements['job_level'],
            'top_required_skills': [skill['skill'] for skill in requirements['required_skills'][:5]],
            'top_preferred_skills': [skill['skill'] for skill in requirements['preferred_skills'][:5]]
        }

        return summary
