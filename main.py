#!/usr/bin/env python3
"""
Main entry point for the resume screening application.
"""
import argparse
import os
import sys
import logging
from datetime import datetime
from typing import List

from config import Config

def setup_logging(verbose: bool = False):
    """Setup logging configuration."""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('resume_screening.log')
        ]
    )

def validate_files(file_paths: List[str]) -> List[str]:
    """Validate that all provided files exist and are supported formats."""
    valid_files = []
    config = Config()
    
    for file_path in file_paths:
        if not os.path.exists(file_path):
            print(f"Warning: File not found: {file_path}")
            continue
        
        _, ext = os.path.splitext(file_path)
        if ext.lower() not in config.SUPPORTED_FORMATS:
            print(f"Warning: Unsupported file format: {file_path}")
            continue
        
        valid_files.append(file_path)
    
    return valid_files

def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Resume Screening Application - Extract skills and experience from resumes or analyze job descriptions (PDF, TXT, DOC, DOCX)"
    )
    
    parser.add_argument(
        'files',
        nargs='*',
        help='Path(s) to file(s) to analyze (PDF, TXT, DOC, DOCX)'
    )
    
    parser.add_argument(
        '-o', '--output',
        type=str,
        help='Output file path for results (default: results.json)'
    )
    
    parser.add_argument(
        '-f', '--format',
        choices=['json', 'csv'],
        default='json',
        help='Output format (default: json)'
    )
    
    parser.add_argument(
        '-m', '--model',
        type=str,
        help='Hugging Face model name to use for analysis'
    )
    
    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    
    parser.add_argument(
        '--list-skills',
        action='store_true',
        help='List all predefined skills and exit'
    )

    parser.add_argument(
        '--mode',
        choices=['resume', 'job'],
        default='resume',
        help='Analysis mode: resume (default) or job description'
    )
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.verbose)
    logger = logging.getLogger(__name__)
    
    # List skills if requested
    if args.list_skills:
        config = Config()
        print("Predefined Skills by Category:")
        print("=" * 40)
        for category, skills in config.SOFTWARE_SKILLS.items():
            print(f"\n{category.upper()}:")
            for skill in sorted(skills):
                print(f"  - {skill}")
        return

    # Check if files are provided when not listing skills
    if not args.files:
        if args.mode == 'resume':
            print("Error: No resume files provided.")
        else:
            print("Error: No job description files provided.")
        print("Supported formats: PDF, TXT, DOC, DOCX")
        sys.exit(1)
    
    # Validate input files
    valid_files = validate_files(args.files)
    if not valid_files:
        print("Error: No valid resume files provided.")
        print("Supported formats: PDF, TXT, DOC, DOCX")
        sys.exit(1)
    
    print(f"Processing {len(valid_files)} resume(s)...")
    
    try:
        # Initialize appropriate screener based on mode
        if args.mode == 'resume':
            from src.resume_screener import ResumeScreener
            screener = ResumeScreener(model_name=args.model)

            # Process resumes
            if len(valid_files) == 1:
                results = screener.process_resume(valid_files[0])
            else:
                results = screener.process_multiple_resumes(valid_files)
        else:  # job mode
            from src.job_description_screener import JobDescriptionScreener
            screener = JobDescriptionScreener(model_name=args.model)

            # Process job descriptions
            if len(valid_files) == 1:
                results = screener.process_job_description(valid_files[0])
            else:
                results = screener.process_multiple_job_descriptions(valid_files)
        
        # Determine output path
        if args.output:
            output_path = args.output
        else:
            output_path = f"results.{args.format}"
        
        # Save results
        if screener.save_results(results, output_path, args.format):
            print(f"Results saved to: {output_path}")
        else:
            print("Error: Failed to save results")
            sys.exit(1)
        
        # Print enhanced summary
        if len(valid_files) == 1:
            if results['success']:
                if args.mode == 'resume':
                    display_resume_results(results)
                else:
                    display_job_description_results(results)
            else:
                if args.mode == 'resume':
                    print(f"Error processing resume: {results.get('error', 'Unknown error')}")
                else:
                    print(f"Error processing job description: {results.get('error', 'Unknown error')}")
        else:
            if args.mode == 'resume':
                print(f"\nProcessed {results['successful']}/{results['total_files']} resumes successfully")
            else:
                print(f"\nProcessed {results['successful']}/{results['total_files']} job descriptions successfully")

    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        print(f"Error: {str(e)}")
        sys.exit(1)

def display_resume_results(results):
    """Display resume analysis results."""
    print(f"\n{'='*80}")
    print(f"                    RESUME ANALYSIS REPORT")
    print(f"{'='*80}")

    # Header with candidate name
    personal_info = results.get('personal_info', {})
    candidate_name = personal_info.get('name', 'Candidate')
    print(f"\nCandidate: {candidate_name}")
    print(f"Analysis Date: {datetime.now().strftime('%B %d, %Y at %I:%M %p')}")
    print(f"Resume File: {os.path.basename(results['file_path'])}")

    # Show basic summary for now
    summary = results.get('summary', {})
    print(f"\nTotal Skills Found: {summary.get('total_skills_found', 0)}")
    print(f"Experience Years: {summary.get('total_experience_years', 'N/A')}")

    # Show top skills
    top_skills = summary.get('top_skills', [])
    if top_skills:
        print(f"\nTop Skills:")
        for i, skill in enumerate(top_skills[:5], 1):
            skill_name = skill.get('skill', skill) if isinstance(skill, dict) else skill
            print(f"{i}. {skill_name}")

    print(f"\n{'─' * 80}")
    print(f"Report generated by Resume Screening AI • {datetime.now().strftime('%B %Y')}")
    print(f"{'─' * 80}")

def display_job_description_results(results):
    """Display job description analysis results."""
    print(f"\n{'='*80}")
    print(f"                JOB DESCRIPTION ANALYSIS REPORT")
    print(f"{'='*80}")

    # Header with job information
    summary = results.get('summary', {})
    job_info = results.get('job_info', {})
    requirements = results.get('requirements', {})

    print(f"\nJob Title: {summary.get('job_title', 'N/A')}")
    print(f"Company: {summary.get('company_name', 'N/A')}")
    print(f"Analysis Date: {datetime.now().strftime('%B %d, %Y at %I:%M %p')}")
    print(f"Job Description File: {os.path.basename(results['file_path'])}")

    # Job Overview Box
    print(f"\n┌─ JOB OVERVIEW " + "─" * 55 + "┐")
    print(f"│ Level: {summary.get('job_level', 'N/A').title():<60} │")
    print(f"│ Location: {summary.get('location', 'N/A'):<56} │")
    print(f"│ Type: {summary.get('employment_type', 'N/A').title():<60} │")
    print(f"└" + "─" * 71 + "┘")

    # Required Skills - Show ALL skills
    required_skills = summary.get('top_required_skills', [])
    ai_required_skills = summary.get('ai_required_skills', [])
    all_required = list(set(required_skills + ai_required_skills))

    # Get detailed required skills from requirements
    requirements = results.get('requirements', {})
    detailed_required = [skill['skill'] for skill in requirements.get('required_skills', [])]
    all_required.extend(detailed_required)
    all_required = list(set(all_required))  # Remove duplicates

    if all_required:
        print(f"\n🎯 REQUIRED SKILLS ({len(all_required)} total)")
        print(f"─" * 50)
        # Display in columns for better readability
        for i, skill in enumerate(sorted(all_required), 1):
            print(f"{i:2d}. {skill}")

    # Preferred Skills - Show ALL skills
    preferred_skills = summary.get('top_preferred_skills', [])
    ai_preferred_skills = summary.get('ai_preferred_skills', [])
    all_preferred = list(set(preferred_skills + ai_preferred_skills))

    # Get detailed preferred skills from requirements
    detailed_preferred = [skill['skill'] for skill in requirements.get('preferred_skills', [])]
    all_preferred.extend(detailed_preferred)
    all_preferred = list(set(all_preferred))  # Remove duplicates

    if all_preferred:
        print(f"\n⭐ PREFERRED SKILLS ({len(all_preferred)} total)")
        print(f"─" * 50)
        for i, skill in enumerate(sorted(all_preferred), 1):
            print(f"{i:2d}. {skill}")

    # Experience Requirements
    exp_years = summary.get('experience_years')
    if exp_years:
        print(f"\n💼 EXPERIENCE REQUIREMENTS")
        print(f"─" * 40)
        print(f"Required: {exp_years} years of experience")

    # Education Requirements
    education_reqs = summary.get('education_requirements', [])
    if education_reqs:
        print(f"\n🎓 EDUCATION REQUIREMENTS")
        print(f"─" * 40)
        for req in education_reqs:
            print(f"• {req}")

    # Certifications
    certifications = summary.get('certifications', [])
    if certifications:
        print(f"\n📜 CERTIFICATIONS")
        print(f"─" * 40)
        for cert in certifications:
            print(f"• {cert}")

    # Soft Skills - Show ALL soft skills
    soft_skills = requirements.get('soft_skills', [])
    if soft_skills:
        print(f"\n🤝 SOFT SKILLS ({len(soft_skills)} total)")
        print(f"─" * 50)
        for i, skill_info in enumerate(soft_skills, 1):
            skill_name = skill_info.get('skill', skill_info) if isinstance(skill_info, dict) else skill_info
            print(f"{i:2d}. {skill_name.title()}")

    # Job Responsibilities
    responsibilities = job_info.get('responsibilities', [])
    if responsibilities:
        print(f"\n📋 KEY RESPONSIBILITIES")
        print(f"─" * 40)
        for i, resp in enumerate(responsibilities[:5], 1):
            print(f"{i}. {resp}")

    # Benefits
    benefits = job_info.get('benefits', [])
    if benefits:
        print(f"\n🎁 BENEFITS & PERKS")
        print(f"─" * 40)
        benefit_text = ", ".join(benefits)
        print(f"{benefit_text}")

    # Salary Information
    salary_range = summary.get('salary_range')
    if salary_range:
        print(f"\n💰 COMPENSATION")
        print(f"─" * 40)
        print(f"Salary Range: ${salary_range['min']:,} - ${salary_range['max']:,} {salary_range.get('currency', 'USD')}")

    # Summary Statistics
    print(f"\n📊 REQUIREMENTS SUMMARY")
    print(f"─" * 40)
    print(f"Total Required Skills: {summary.get('total_required_skills', 0)}")
    print(f"Total Preferred Skills: {summary.get('total_preferred_skills', 0)}")
    print(f"Soft Skills Mentioned: {summary.get('soft_skills_count', 0)}")
    print(f"Has Experience Requirements: {'Yes' if summary.get('has_experience_requirements') else 'No'}")
    print(f"Has Education Requirements: {'Yes' if summary.get('has_education_requirements') else 'No'}")
    print(f"Has Certifications: {'Yes' if summary.get('has_certifications') else 'No'}")

    print(f"\n{'─' * 80}")
    print(f"Report generated by Job Description Analysis AI • {datetime.now().strftime('%B %Y')}")
    print(f"{'─' * 80}")

if __name__ == "__main__":
    main()
