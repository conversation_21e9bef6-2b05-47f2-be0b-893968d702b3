{"file_path": "sample_job_description.txt", "timestamp": "2025-06-24T12:51:12.648649", "success": true, "error": null, "extraction_method": "txt_reader", "raw_text": "Senior Software Engineer - Full Stack Development Company: Tech Corp Solutions Location: San Francisco, CA (Remote options available) Employment Type: Full-time Salary: $120, 000 - $160, 000 per year About Tech Corp Solutions: Tech Corp Solutions is a leading technology company specializing in innovative software solutions for enterprise clients.\nWe are a fast-growing startup with over 200 employees, focused on delivering cutting-edge products that transform how businesses operate.\nJob Description: We are seeking a highly skilled Senior Software Engineer to join our dynamic development team.\nThe ideal candidate will have extensive experience in full-stack development and a passion for building scalable, high-performance applications.\nKey Responsibilities: • Design and develop robust, scalable web applications using modern technologies • Collaborate with cross-functional teams to define, design, and ship new features • Write clean, maintainable, and efficient code following best practices • Participate in code reviews and provide constructive feedback to team members • Troubleshoot and debug applications to optimize performance • Mentor junior developers and contribute to team knowledge sharing Required Qualifications: • Bachelor's degree in Computer Science, Engineering, or related field • 5+ years of professional software development experience • Strong proficiency in Java Script, Type Script, and modern frameworks (React, Angular, or Vue. js) • Solid experience with backend technologies (Node. js, Python, or Java) • Experience with relational databases (Postgre SQL, My SQL) and No SQL databases (Mongo DB) • Proficiency with version control systems (Git) • Experience with cloud platforms (AWS, Azure, or Google Cloud Platform) • Knowledge of containerization technologies (Docker, Kubernetes) • Understanding of CI/CD pipelines and Dev Ops practices • Strong problem-solving skills and attention to detail • Excellent communication and teamwork abilities Preferred Qualifications: • Master's degree in Computer Science or related field • Experience with microservices architecture • Knowledge of machine learning and data science concepts • Familiarity with mobile development (React Native, Flutter) • Experience with testing frameworks (Jest, Cypress, Selenium) • AWS or Azure certifications • Experience with agile development methodologies • Leadership experience or mentoring background Technical Skills Required: • Programming Languages: Java Script, Type Script, Python, Java • Frontend: React, HTML5, CSS3, SASS/LESS, Webpack • Backend: Node. js, Express. js, RESTful APIs, Graph QL • Databases: Postgre SQL, Mongo DB, Redis • Cloud: AWS (EC2, S3, Lambda, RDS), Docker, Kubernetes • Tools: Git, Jenkins, JIRA, Slack Nice to Have: • Experience with React Native for mobile development • Knowledge of blockchain technologies • Familiarity with AI/ML frameworks (Tensor Flow, Py Torch) • Experience with data visualization tools (D3. js, Chart. js) Benefits: • Competitive salary and equity package • Comprehensive health, dental, and vision insurance • 401(k) with company matching • Flexible work hours and remote work options • Professional development budget ($2, 000 annually) • Unlimited PTO policy • Modern office with free snacks and beverages How to Apply: Please submit your resume, cover letter, and portfolio showcasing your best work.\nWe are an equal opportunity employer committed to diversity and inclusion.", "entities": [{"word": "Senior Software", "entity_group": "PERSON", "score": 0.9, "start": 0, "end": 15}, {"word": "Full Stack", "entity_group": "PERSON", "score": 0.9, "start": 27, "end": 37}, {"word": "Development Company", "entity_group": "PERSON", "score": 0.9, "start": 38, "end": 57}, {"word": "Tech Corp", "entity_group": "PERSON", "score": 0.9, "start": 59, "end": 68}, {"word": "Solutions Location", "entity_group": "PERSON", "score": 0.9, "start": 69, "end": 87}, {"word": "San Francisco", "entity_group": "PERSON", "score": 0.9, "start": 89, "end": 102}, {"word": "Remote options", "entity_group": "PERSON", "score": 0.9, "start": 108, "end": 122}, {"word": "Employment Type", "entity_group": "PERSON", "score": 0.9, "start": 134, "end": 149}, {"word": "time Salary", "entity_group": "PERSON", "score": 0.9, "start": 156, "end": 167}, {"word": "per year", "entity_group": "PERSON", "score": 0.9, "start": 191, "end": 199}, {"word": "About Tech", "entity_group": "PERSON", "score": 0.9, "start": 200, "end": 210}, {"word": "Corp Solutions", "entity_group": "PERSON", "score": 0.9, "start": 211, "end": 225}, {"word": "Tech Corp", "entity_group": "PERSON", "score": 0.9, "start": 227, "end": 236}, {"word": "Solutions is", "entity_group": "PERSON", "score": 0.9, "start": 237, "end": 249}, {"word": "leading technology", "entity_group": "PERSON", "score": 0.9, "start": 252, "end": 270}, {"word": "company specializing", "entity_group": "PERSON", "score": 0.9, "start": 271, "end": 291}, {"word": "in innovative", "entity_group": "PERSON", "score": 0.9, "start": 292, "end": 305}, {"word": "software solutions", "entity_group": "PERSON", "score": 0.9, "start": 306, "end": 324}, {"word": "for enterprise", "entity_group": "PERSON", "score": 0.9, "start": 325, "end": 339}, {"word": "We are", "entity_group": "PERSON", "score": 0.9, "start": 349, "end": 355}, {"word": "growing startup", "entity_group": "PERSON", "score": 0.9, "start": 363, "end": 378}, {"word": "with over", "entity_group": "PERSON", "score": 0.9, "start": 379, "end": 388}, {"word": "focused on", "entity_group": "PERSON", "score": 0.9, "start": 404, "end": 414}, {"word": "delivering cutting", "entity_group": "PERSON", "score": 0.9, "start": 415, "end": 433}, {"word": "edge products", "entity_group": "PERSON", "score": 0.9, "start": 434, "end": 447}, {"word": "that transform", "entity_group": "PERSON", "score": 0.9, "start": 448, "end": 462}, {"word": "how businesses", "entity_group": "PERSON", "score": 0.9, "start": 463, "end": 477}, {"word": "Job Description", "entity_group": "PERSON", "score": 0.9, "start": 487, "end": 502}, {"word": "We are", "entity_group": "PERSON", "score": 0.9, "start": 504, "end": 510}, {"word": "highly skilled", "entity_group": "PERSON", "score": 0.9, "start": 521, "end": 535}, {"word": "Senior Software", "entity_group": "PERSON", "score": 0.9, "start": 536, "end": 551}, {"word": "Engineer to", "entity_group": "PERSON", "score": 0.9, "start": 552, "end": 563}, {"word": "join our", "entity_group": "PERSON", "score": 0.9, "start": 564, "end": 572}, {"word": "dynamic development", "entity_group": "PERSON", "score": 0.9, "start": 573, "end": 592}, {"word": "The ideal", "entity_group": "PERSON", "score": 0.9, "start": 599, "end": 608}, {"word": "candidate will", "entity_group": "PERSON", "score": 0.9, "start": 609, "end": 623}, {"word": "have extensive", "entity_group": "PERSON", "score": 0.9, "start": 624, "end": 638}, {"word": "experience in", "entity_group": "PERSON", "score": 0.9, "start": 639, "end": 652}, {"word": "stack development", "entity_group": "PERSON", "score": 0.9, "start": 658, "end": 675}, {"word": "passion for", "entity_group": "PERSON", "score": 0.9, "start": 682, "end": 693}, {"word": "building scalable", "entity_group": "PERSON", "score": 0.9, "start": 694, "end": 711}, {"word": "performance applications", "entity_group": "PERSON", "score": 0.9, "start": 718, "end": 742}, {"word": "Key Responsibilities", "entity_group": "PERSON", "score": 0.9, "start": 744, "end": 764}, {"word": "Design and", "entity_group": "PERSON", "score": 0.9, "start": 768, "end": 778}, {"word": "develop robust", "entity_group": "PERSON", "score": 0.9, "start": 779, "end": 793}, {"word": "scalable web", "entity_group": "PERSON", "score": 0.9, "start": 795, "end": 807}, {"word": "applications using", "entity_group": "PERSON", "score": 0.9, "start": 808, "end": 826}, {"word": "modern technologies", "entity_group": "PERSON", "score": 0.9, "start": 827, "end": 846}, {"word": "Collaborate with", "entity_group": "PERSON", "score": 0.9, "start": 849, "end": 865}, {"word": "functional teams", "entity_group": "PERSON", "score": 0.9, "start": 872, "end": 888}, {"word": "to define", "entity_group": "PERSON", "score": 0.9, "start": 889, "end": 898}, {"word": "and ship", "entity_group": "PERSON", "score": 0.9, "start": 908, "end": 916}, {"word": "new features", "entity_group": "PERSON", "score": 0.9, "start": 917, "end": 929}, {"word": "Write clean", "entity_group": "PERSON", "score": 0.9, "start": 932, "end": 943}, {"word": "and efficient", "entity_group": "PERSON", "score": 0.9, "start": 959, "end": 972}, {"word": "code following", "entity_group": "PERSON", "score": 0.9, "start": 973, "end": 987}, {"word": "best practices", "entity_group": "PERSON", "score": 0.9, "start": 988, "end": 1002}, {"word": "Participate in", "entity_group": "PERSON", "score": 0.9, "start": 1005, "end": 1019}, {"word": "code reviews", "entity_group": "PERSON", "score": 0.9, "start": 1020, "end": 1032}, {"word": "and provide", "entity_group": "PERSON", "score": 0.9, "start": 1033, "end": 1044}, {"word": "constructive feedback", "entity_group": "PERSON", "score": 0.9, "start": 1045, "end": 1066}, {"word": "to team", "entity_group": "PERSON", "score": 0.9, "start": 1067, "end": 1074}, {"word": "Troubleshoot and", "entity_group": "PERSON", "score": 0.9, "start": 1085, "end": 1101}, {"word": "debug applications", "entity_group": "PERSON", "score": 0.9, "start": 1102, "end": 1120}, {"word": "to optimize", "entity_group": "PERSON", "score": 0.9, "start": 1121, "end": 1132}, {"word": "Mentor junior", "entity_group": "PERSON", "score": 0.9, "start": 1147, "end": 1160}, {"word": "developers and", "entity_group": "PERSON", "score": 0.9, "start": 1161, "end": 1175}, {"word": "contribute to", "entity_group": "PERSON", "score": 0.9, "start": 1176, "end": 1189}, {"word": "team knowledge", "entity_group": "PERSON", "score": 0.9, "start": 1190, "end": 1204}, {"word": "sharing Required", "entity_group": "PERSON", "score": 0.9, "start": 1205, "end": 1221}, {"word": "degree in", "entity_group": "PERSON", "score": 0.9, "start": 1251, "end": 1260}, {"word": "Computer Science", "entity_group": "PERSON", "score": 0.9, "start": 1261, "end": 1277}, {"word": "or related", "entity_group": "PERSON", "score": 0.9, "start": 1292, "end": 1302}, {"word": "years of", "entity_group": "PERSON", "score": 0.9, "start": 1314, "end": 1322}, {"word": "professional software", "entity_group": "PERSON", "score": 0.9, "start": 1323, "end": 1344}, {"word": "development experience", "entity_group": "PERSON", "score": 0.9, "start": 1345, "end": 1367}, {"word": "Strong proficiency", "entity_group": "PERSON", "score": 0.9, "start": 1370, "end": 1388}, {"word": "in Java", "entity_group": "PERSON", "score": 0.9, "start": 1389, "end": 1396}, {"word": "Type Script", "entity_group": "PERSON", "score": 0.9, "start": 1405, "end": 1416}, {"word": "and modern", "entity_group": "PERSON", "score": 0.9, "start": 1418, "end": 1428}, {"word": "or Vue", "entity_group": "PERSON", "score": 0.9, "start": 1457, "end": 1463}, {"word": "Solid experience", "entity_group": "PERSON", "score": 0.9, "start": 1471, "end": 1487}, {"word": "with backend", "entity_group": "PERSON", "score": 0.9, "start": 1488, "end": 1500}, {"word": "or Java", "entity_group": "PERSON", "score": 0.9, "start": 1533, "end": 1540}, {"word": "Experience with", "entity_group": "PERSON", "score": 0.9, "start": 1544, "end": 1559}, {"word": "relational databases", "entity_group": "PERSON", "score": 0.9, "start": 1560, "end": 1580}, {"word": "Postgre SQL", "entity_group": "PERSON", "score": 0.9, "start": 1582, "end": 1593}, {"word": "My SQL", "entity_group": "PERSON", "score": 0.9, "start": 1595, "end": 1601}, {"word": "and No", "entity_group": "PERSON", "score": 0.9, "start": 1603, "end": 1609}, {"word": "SQL databases", "entity_group": "PERSON", "score": 0.9, "start": 1610, "end": 1623}, {"word": "Mongo DB", "entity_group": "PERSON", "score": 0.9, "start": 1625, "end": 1633}, {"word": "Proficiency with", "entity_group": "PERSON", "score": 0.9, "start": 1637, "end": 1653}, {"word": "version control", "entity_group": "PERSON", "score": 0.9, "start": 1654, "end": 1669}, {"word": "Experience with", "entity_group": "PERSON", "score": 0.9, "start": 1686, "end": 1701}, {"word": "cloud platforms", "entity_group": "PERSON", "score": 0.9, "start": 1702, "end": 1717}, {"word": "or Google", "entity_group": "PERSON", "score": 0.9, "start": 1731, "end": 1740}, {"word": "Cloud Platform", "entity_group": "PERSON", "score": 0.9, "start": 1741, "end": 1755}, {"word": "Knowledge of", "entity_group": "PERSON", "score": 0.9, "start": 1759, "end": 1771}, {"word": "containerization technologies", "entity_group": "PERSON", "score": 0.9, "start": 1772, "end": 1801}, {"word": "Understanding of", "entity_group": "PERSON", "score": 0.9, "start": 1825, "end": 1841}, {"word": "CD pipelines", "entity_group": "PERSON", "score": 0.9, "start": 1845, "end": 1857}, {"word": "and <PERSON>", "entity_group": "PERSON", "score": 0.9, "start": 1858, "end": 1865}, {"word": "Ops practices", "entity_group": "PERSON", "score": 0.9, "start": 1866, "end": 1879}, {"word": "Strong problem", "entity_group": "PERSON", "score": 0.9, "start": 1882, "end": 1896}, {"word": "solving skills", "entity_group": "PERSON", "score": 0.9, "start": 1897, "end": 1911}, {"word": "and attention", "entity_group": "PERSON", "score": 0.9, "start": 1912, "end": 1925}, {"word": "to detail", "entity_group": "PERSON", "score": 0.9, "start": 1926, "end": 1935}, {"word": "Excellent communication", "entity_group": "PERSON", "score": 0.9, "start": 1938, "end": 1961}, {"word": "and teamwork", "entity_group": "PERSON", "score": 0.9, "start": 1962, "end": 1974}, {"word": "abilities Preferred", "entity_group": "PERSON", "score": 0.9, "start": 1975, "end": 1994}, {"word": "degree in", "entity_group": "PERSON", "score": 0.9, "start": 2022, "end": 2031}, {"word": "Computer Science", "entity_group": "PERSON", "score": 0.9, "start": 2032, "end": 2048}, {"word": "or related", "entity_group": "PERSON", "score": 0.9, "start": 2049, "end": 2059}, {"word": "Experience with", "entity_group": "PERSON", "score": 0.9, "start": 2068, "end": 2083}, {"word": "microservices architecture", "entity_group": "PERSON", "score": 0.9, "start": 2084, "end": 2110}, {"word": "Knowledge of", "entity_group": "PERSON", "score": 0.9, "start": 2113, "end": 2125}, {"word": "machine learning", "entity_group": "PERSON", "score": 0.9, "start": 2126, "end": 2142}, {"word": "and data", "entity_group": "PERSON", "score": 0.9, "start": 2143, "end": 2151}, {"word": "science concepts", "entity_group": "PERSON", "score": 0.9, "start": 2152, "end": 2168}, {"word": "Familiarity with", "entity_group": "PERSON", "score": 0.9, "start": 2171, "end": 2187}, {"word": "mobile development", "entity_group": "PERSON", "score": 0.9, "start": 2188, "end": 2206}, {"word": "React Native", "entity_group": "PERSON", "score": 0.9, "start": 2208, "end": 2220}, {"word": "Experience with", "entity_group": "PERSON", "score": 0.9, "start": 2233, "end": 2248}, {"word": "testing frameworks", "entity_group": "PERSON", "score": 0.9, "start": 2249, "end": 2267}, {"word": "AWS or", "entity_group": "PERSON", "score": 0.9, "start": 2296, "end": 2302}, {"word": "Azure certifications", "entity_group": "PERSON", "score": 0.9, "start": 2303, "end": 2323}, {"word": "Experience with", "entity_group": "PERSON", "score": 0.9, "start": 2326, "end": 2341}, {"word": "agile development", "entity_group": "PERSON", "score": 0.9, "start": 2342, "end": 2359}, {"word": "Leadership experience", "entity_group": "PERSON", "score": 0.9, "start": 2376, "end": 2397}, {"word": "or mentoring", "entity_group": "PERSON", "score": 0.9, "start": 2398, "end": 2410}, {"word": "background Technical", "entity_group": "PERSON", "score": 0.9, "start": 2411, "end": 2431}, {"word": "Skills Required", "entity_group": "PERSON", "score": 0.9, "start": 2432, "end": 2447}, {"word": "Programming Languages", "entity_group": "PERSON", "score": 0.9, "start": 2451, "end": 2472}, {"word": "Java Script", "entity_group": "PERSON", "score": 0.9, "start": 2474, "end": 2485}, {"word": "Type Script", "entity_group": "PERSON", "score": 0.9, "start": 2487, "end": 2498}, {"word": "RESTful APIs", "entity_group": "PERSON", "score": 0.9, "start": 2598, "end": 2610}, {"word": "Graph QL", "entity_group": "PERSON", "score": 0.9, "start": 2612, "end": 2620}, {"word": "Postgre SQL", "entity_group": "PERSON", "score": 0.9, "start": 2634, "end": 2645}, {"word": "Mongo DB", "entity_group": "PERSON", "score": 0.9, "start": 2647, "end": 2655}, {"word": "Slack Nice", "entity_group": "PERSON", "score": 0.9, "start": 2748, "end": 2758}, {"word": "to Have", "entity_group": "PERSON", "score": 0.9, "start": 2759, "end": 2766}, {"word": "Experience with", "entity_group": "PERSON", "score": 0.9, "start": 2770, "end": 2785}, {"word": "React Native", "entity_group": "PERSON", "score": 0.9, "start": 2786, "end": 2798}, {"word": "for mobile", "entity_group": "PERSON", "score": 0.9, "start": 2799, "end": 2809}, {"word": "Knowledge of", "entity_group": "PERSON", "score": 0.9, "start": 2824, "end": 2836}, {"word": "blockchain technologies", "entity_group": "PERSON", "score": 0.9, "start": 2837, "end": 2860}, {"word": "Familiarity with", "entity_group": "PERSON", "score": 0.9, "start": 2863, "end": 2879}, {"word": "ML frameworks", "entity_group": "PERSON", "score": 0.9, "start": 2883, "end": 2896}, {"word": "Tensor Flow", "entity_group": "PERSON", "score": 0.9, "start": 2898, "end": 2909}, {"word": "<PERSON><PERSON>", "entity_group": "PERSON", "score": 0.9, "start": 2911, "end": 2919}, {"word": "Experience with", "entity_group": "PERSON", "score": 0.9, "start": 2923, "end": 2938}, {"word": "data visualization", "entity_group": "PERSON", "score": 0.9, "start": 2939, "end": 2957}, {"word": "Competitive salary", "entity_group": "PERSON", "score": 0.9, "start": 2996, "end": 3014}, {"word": "and equity", "entity_group": "PERSON", "score": 0.9, "start": 3015, "end": 3025}, {"word": "Comprehensive health", "entity_group": "PERSON", "score": 0.9, "start": 3036, "end": 3056}, {"word": "and vision", "entity_group": "PERSON", "score": 0.9, "start": 3066, "end": 3076}, {"word": "with company", "entity_group": "PERSON", "score": 0.9, "start": 3096, "end": 3108}, {"word": "Flexible work", "entity_group": "PERSON", "score": 0.9, "start": 3120, "end": 3133}, {"word": "hours and", "entity_group": "PERSON", "score": 0.9, "start": 3134, "end": 3143}, {"word": "remote work", "entity_group": "PERSON", "score": 0.9, "start": 3144, "end": 3155}, {"word": "Professional development", "entity_group": "PERSON", "score": 0.9, "start": 3166, "end": 3190}, {"word": "Unlimited PTO", "entity_group": "PERSON", "score": 0.9, "start": 3219, "end": 3232}, {"word": "Modern office", "entity_group": "PERSON", "score": 0.9, "start": 3242, "end": 3255}, {"word": "with free", "entity_group": "PERSON", "score": 0.9, "start": 3256, "end": 3265}, {"word": "snacks and", "entity_group": "PERSON", "score": 0.9, "start": 3266, "end": 3276}, {"word": "beverages How", "entity_group": "PERSON", "score": 0.9, "start": 3277, "end": 3290}, {"word": "to Apply", "entity_group": "PERSON", "score": 0.9, "start": 3291, "end": 3299}, {"word": "Please submit", "entity_group": "PERSON", "score": 0.9, "start": 3301, "end": 3314}, {"word": "your resume", "entity_group": "PERSON", "score": 0.9, "start": 3315, "end": 3326}, {"word": "cover letter", "entity_group": "PERSON", "score": 0.9, "start": 3328, "end": 3340}, {"word": "and portfolio", "entity_group": "PERSON", "score": 0.9, "start": 3342, "end": 3355}, {"word": "showcasing your", "entity_group": "PERSON", "score": 0.9, "start": 3356, "end": 3371}, {"word": "best work", "entity_group": "PERSON", "score": 0.9, "start": 3372, "end": 3381}, {"word": "We are", "entity_group": "PERSON", "score": 0.9, "start": 3383, "end": 3389}, {"word": "an equal", "entity_group": "PERSON", "score": 0.9, "start": 3390, "end": 3398}, {"word": "opportunity employer", "entity_group": "PERSON", "score": 0.9, "start": 3399, "end": 3419}, {"word": "committed to", "entity_group": "PERSON", "score": 0.9, "start": 3420, "end": 3432}, {"word": "diversity and", "entity_group": "PERSON", "score": 0.9, "start": 3433, "end": 3446}, {"word": "Company", "entity_group": "ORG", "score": 0.9, "start": 50, "end": 57}, {"word": "Corp", "entity_group": "ORG", "score": 0.9, "start": 64, "end": 68}, {"word": "Solutions", "entity_group": "ORG", "score": 0.9, "start": 69, "end": 78}, {"word": "Corp", "entity_group": "ORG", "score": 0.9, "start": 211, "end": 215}, {"word": "Solutions", "entity_group": "ORG", "score": 0.9, "start": 216, "end": 225}, {"word": "Corp", "entity_group": "ORG", "score": 0.9, "start": 232, "end": 236}, {"word": "Solutions", "entity_group": "ORG", "score": 0.9, "start": 237, "end": 246}, {"word": "company", "entity_group": "ORG", "score": 0.9, "start": 271, "end": 278}, {"word": "solutions", "entity_group": "ORG", "score": 0.9, "start": 315, "end": 324}, {"word": "technologies", "entity_group": "ORG", "score": 0.9, "start": 834, "end": 846}, {"word": "technologies", "entity_group": "ORG", "score": 0.9, "start": 1501, "end": 1513}, {"word": "systems", "entity_group": "ORG", "score": 0.9, "start": 1670, "end": 1677}, {"word": "technologies", "entity_group": "ORG", "score": 0.9, "start": 1789, "end": 1801}, {"word": "technologies", "entity_group": "ORG", "score": 0.9, "start": 2848, "end": 2860}, {"word": "company", "entity_group": "ORG", "score": 0.9, "start": 3101, "end": 3108}, {"word": "Java", "entity_group": "SKILL", "score": 0.9, "start": 1392, "end": 1396}, {"word": "React", "entity_group": "SKILL", "score": 0.9, "start": 1441, "end": 1446}, {"word": "Python", "entity_group": "SKILL", "score": 0.9, "start": 1525, "end": 1531}, {"word": "Java", "entity_group": "SKILL", "score": 0.9, "start": 1536, "end": 1540}, {"word": "SQL", "entity_group": "SKILL", "score": 0.9, "start": 1590, "end": 1593}, {"word": "SQL", "entity_group": "SKILL", "score": 0.9, "start": 1598, "end": 1601}, {"word": "SQL", "entity_group": "SKILL", "score": 0.9, "start": 1610, "end": 1613}, {"word": "AWS", "entity_group": "SKILL", "score": 0.9, "start": 1719, "end": 1722}, {"word": "<PERSON>er", "entity_group": "SKILL", "score": 0.9, "start": 1803, "end": 1809}, {"word": "Kubernetes", "entity_group": "SKILL", "score": 0.9, "start": 1811, "end": 1821}, {"word": "React", "entity_group": "SKILL", "score": 0.9, "start": 2208, "end": 2213}, {"word": "AWS", "entity_group": "SKILL", "score": 0.9, "start": 2296, "end": 2299}, {"word": "Java", "entity_group": "SKILL", "score": 0.9, "start": 2474, "end": 2478}, {"word": "Python", "entity_group": "SKILL", "score": 0.9, "start": 2500, "end": 2506}, {"word": "Java", "entity_group": "SKILL", "score": 0.9, "start": 2508, "end": 2512}, {"word": "React", "entity_group": "SKILL", "score": 0.9, "start": 2525, "end": 2530}, {"word": "SQL", "entity_group": "SKILL", "score": 0.9, "start": 2642, "end": 2645}, {"word": "AWS", "entity_group": "SKILL", "score": 0.9, "start": 2672, "end": 2675}, {"word": "<PERSON>er", "entity_group": "SKILL", "score": 0.9, "start": 2700, "end": 2706}, {"word": "Kubernetes", "entity_group": "SKILL", "score": 0.9, "start": 2708, "end": 2718}, {"word": "React", "entity_group": "SKILL", "score": 0.9, "start": 2786, "end": 2791}], "job_info": {"title": null, "company": "Tech Corp Solutions Location: San Francisco, CA (Remote options available) Employment Type: Full-time Salary: $120, 000 - $160, 000 per year About Tech Corp Solutions: Tech Corp Solutions is a leading technology company specializing in innovative software solutions for enterprise clients.", "location": "San Francisco, CA (Remote options available) Employment Type: Full-time Salary: $120, 000 - $160, 000 per year About Tech Corp Solutions: Tech Corp Solutions is a leading technology company specializing in innovative software solutions for enterprise clients.", "employment_type": "full-time", "salary_range": null, "responsibilities": ["Design and develop robust, scalable web applications using modern technologies", "Collaborate with cross", "functional teams to define, design, and ship new features", "Write clean, maintainable, and efficient code following best practices", "Participate in code reviews and provide constructive feedback to team members"], "benefits": ["dental", "vision", "pto", "remote work"]}, "requirements": {"required_skills": [{"skill": "mongodb", "context": "bases (Postgre SQL, My SQL) and No SQL databases (Mongo DB) • Proficiency with version control systems (G", "section": "full_text", "confidence": 1.0}, {"skill": "google cloud platform", "context": "• Experience with cloud platforms (AWS, Azure, or Google Cloud Platform) • Knowledge of containerization technologies (Do", "section": "full_text", "confidence": 1.0}, {"skill": "javascript", "context": "t, and modern frameworks (React, Angular, or Vue. js) • Solid experience with backend technologies (No", "section": "full_text", "confidence": 0.8999999999999999}, {"skill": "python", "context": "d experience with backend technologies (Node. js, Python, or Java) • Experience with relational databases", "section": "full_text", "confidence": 0.8999999999999999}, {"skill": "java", "context": "re development experience • Strong proficiency in Java Script, Type Script, and modern frameworks (React", "section": "full_text", "confidence": 0.8999999999999999}, {"skill": "sass", "context": "ipt, Python, Java • Frontend: React, HTML5, CSS3, SASS/LESS, Webpack • Backend: Node. js, Express. js, R", "section": "full_text", "confidence": 0.8999999999999999}, {"skill": "less", "context": "Python, Java • Frontend: React, HTML5, CSS3, SASS/LESS, Webpack • Backend: Node. js, Express. js, RESTfu", "section": "full_text", "confidence": 0.8999999999999999}, {"skill": "sql", "context": ") • Experience with relational databases (Postgre SQL, My SQL) and No SQL databases (Mongo DB) • Profic", "section": "full_text", "confidence": 0.8999999999999999}, {"skill": "react", "context": "Java Script, Type Script, and modern frameworks (React, Angular, or Vue. js) • Solid experience with bac", "section": "full_text", "confidence": 0.8999999999999999}, {"skill": "vue", "context": "Script, and modern frameworks (React, Angular, or Vue. js) • Solid experience with backend technologies", "section": "full_text", "confidence": 0.8999999999999999}, {"skill": "angular", "context": "cript, <PERSON> Script, and modern frameworks (React, Angular, or Vue. js) • Solid experience with backend tech", "section": "full_text", "confidence": 0.8999999999999999}, {"skill": "express", "context": "L5, CSS3, SASS/LESS, Webpack • Backend: Node. js, Express. js, RESTful APIs, Graph QL • Databases: Postgre", "section": "full_text", "confidence": 0.8999999999999999}, {"skill": "flutter", "context": "amiliarity with mobile development (React Native, Flutter) • Experience with testing frameworks (<PERSON><PERSON>, Cypr", "section": "full_text", "confidence": 0.8999999999999999}, {"skill": "redis", "context": "PIs, Graph QL • Databases: Postgre SQL, Mongo DB, Redis • Cloud: AWS (EC2, S3, Lambda, RDS), <PERSON><PERSON>, <PERSON><PERSON>", "section": "full_text", "confidence": 0.8999999999999999}, {"skill": "azure", "context": "ems (Git) • Experience with cloud platforms (AWS, Azure, or Google Cloud Platform) • Knowledge of contain", "section": "full_text", "confidence": 0.8999999999999999}, {"skill": "microsoft azure", "context": "ems (Git) • Experience with cloud platforms (AWS, Azure, or Google Cloud Platform) • Knowledge of contain", "section": "full_text", "confidence": 0.8999999999999999}, {"skill": "google cloud", "context": "• Experience with cloud platforms (AWS, Azure, or Google Cloud Platform) • Knowledge of containerization technol", "section": "full_text", "confidence": 0.8999999999999999}, {"skill": "webpack", "context": ", Java • Frontend: React, HTML5, CSS3, SASS/LESS, Webpack • Backend: Node. js, Express. js, RESTful APIs, G", "section": "full_text", "confidence": 0.8999999999999999}, {"skill": "jest", "context": "e, Flutter) • Experience with testing frameworks (Jest, Cypress, Selenium) • AWS or Azure certifications", "section": "full_text", "confidence": 0.8999999999999999}, {"skill": "cypress", "context": "tter) • Experience with testing frameworks (Jest, Cypress, Selenium) • AWS or Azure certifications • Experi", "section": "full_text", "confidence": 0.8999999999999999}, {"skill": "selenium", "context": "xperience with testing frameworks (Jest, Cypress, Selenium) • AWS or Azure certifications • Experience with", "section": "full_text", "confidence": 0.8999999999999999}, {"skill": "agile", "context": ") • AWS or Azure certifications • Experience with agile development methodologies • Leadership experience", "section": "full_text", "confidence": 0.8999999999999999}, {"skill": "mentoring", "context": "elopment methodologies • Leadership experience or mentoring background Technical Skills Required: • Programmi", "section": "full_text", "confidence": 0.8999999999999999}, {"skill": "typescript", "context": "innovative software solutions for enterprise clients.\nWe are a fast-growing startup with over 200 empl", "section": "full_text", "confidence": 0.7999999999999999}, {"skill": "go", "context": "es (Postgre SQL, My SQL) and No SQL databases (Mongo DB) • Proficiency with version control systems (G", "section": "full_text", "confidence": 0.7999999999999999}, {"skill": "html", "context": "ipt, Type Script, Python, Java • Frontend: React, HTML5, CSS3, SASS/LESS, Webpack • Backend: Node. js, E", "section": "full_text", "confidence": 0.7999999999999999}, {"skill": "css", "context": "<PERSON><PERSON>, Python, Java • Frontend: React, HTML5, CSS3, SASS/LESS, Webpack • Backend: Node. js, Express", "section": "full_text", "confidence": 0.7999999999999999}, {"skill": "unity", "context": "showcasing your best work.\nWe are an equal opportunity employer committed to diversity and inclusion.", "section": "full_text", "confidence": 0.7999999999999999}, {"skill": "pip", "context": "ies (<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>) • Understanding of CI/CD pipelines and Dev Ops practices • Strong problem-solv", "section": "full_text", "confidence": 0.7999999999999999}, {"skill": "defi", "context": "gies • Collaborate with cross-functional teams to define, design, and ship new features • Write clean, m", "section": "full_text", "confidence": 0.7999999999999999}, {"skill": "lean", "context": "to define, design, and ship new features • Write clean, maintainable, and efficient code following best", "section": "full_text", "confidence": 0.7999999999999999}, {"skill": "code review", "context": "nt code following best practices • Participate in code reviews and provide constructive feedback to team member", "section": "full_text", "confidence": 0.7999999999999999}, {"skill": "rest", "context": "S/LESS, Webpack • Backend: Node. js, Express. js, RESTful APIs, Graph QL • Databases: Postgre SQL, Mongo", "section": "full_text", "confidence": 0.7999999999999999}], "preferred_skills": [{"skill": "docker", "context": "rm) • Knowledge of containerization technologies (Docker, Kubernetes) • Understanding of CI/CD pipelines a", "section": "full_text", "confidence": 1.0}, {"skill": "react native", "context": "e concepts • Familiarity with mobile development (React Native, Flutter) • Experience with testing frameworks (J", "section": "full_text", "confidence": 0.8999999999999999}, {"skill": "kubernetes", "context": "owledge of containerization technologies (Docker, Kubernetes) • Understanding of CI/CD pipelines and Dev Ops p", "section": "full_text", "confidence": 0.8999999999999999}, {"skill": "aws", "context": "systems (Git) • Experience with cloud platforms (AWS, Azure, or Google Cloud Platform) • Knowledge of", "section": "full_text", "confidence": 0.8999999999999999}, {"skill": "amazon web services", "context": "systems (Git) • Experience with cloud platforms (AWS, Azure, or Google Cloud Platform) • Knowledge of", "section": "full_text", "confidence": 0.8999999999999999}, {"skill": "jenkins", "context": "3, <PERSON><PERSON>, <PERSON><PERSON>), <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> • Tools: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> to Have: • Experience with <PERSON><PERSON>", "section": "full_text", "confidence": 0.8999999999999999}, {"skill": "git", "context": "o DB) • Proficiency with version control systems (Git) • Experience with cloud platforms (AWS, Azure, o", "section": "full_text", "confidence": 0.8999999999999999}, {"skill": "jira", "context": ", R<PERSON>), <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> • Tools: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> to Have: • Experience with <PERSON><PERSON><PERSON>", "section": "full_text", "confidence": 0.8999999999999999}, {"skill": "slack", "context": ", <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> • Tools: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> to Have: • Experience with React Native for", "section": "full_text", "confidence": 0.8999999999999999}, {"skill": "blockchain", "context": "eact Native for mobile development • Knowledge of blockchain technologies • Familiarity with AI/ML frameworks", "section": "full_text", "confidence": 0.8999999999999999}, {"skill": "microservices", "context": "mputer Science or related field • Experience with microservices architecture • Knowledge of machine learning and", "section": "full_text", "confidence": 0.8999999999999999}, {"skill": "scala", "context": "full-stack development and a passion for building scalable, high-performance applications.\nKey Responsibi", "section": "full_text", "confidence": 0.7999999999999999}, {"skill": "r", "context": "Senior Software Engineer - Full Stack Development Compan", "section": "full_text", "confidence": 0.7999999999999999}, {"skill": "c", "context": "Senior Software Engineer - Full Stack Development Company: Tech Corp Solutions Locatio", "section": "full_text", "confidence": 0.7999999999999999}, {"skill": "v", "context": "Senior Software Engineer - Full Stack Development Company: Tech Corp Solutions Location: Sa", "section": "full_text", "confidence": 0.7999999999999999}, {"skill": "lit", "context": "ble, high-performance applications.\nKey Responsibilities: • Design and develop robust, scalable web app", "section": "full_text", "confidence": 0.7999999999999999}, {"skill": "gin", "context": "Senior Software Engineer - Full Stack Development Company: Tech Corp So", "section": "full_text", "confidence": 0.7999999999999999}, {"skill": "chai", "context": "Native for mobile development • Knowledge of blockchain technologies • Familiarity with AI/ML frameworks", "section": "full_text", "confidence": 0.7999999999999999}], "experience_requirements": {"required_years": null, "preferred_years": null, "domains": []}, "education_requirements": [{"requirement": "bachelor's degree in computer science, engineering, or related field • 5+ years of professional software development experience • strong proficiency in java script, type script, and modern frameworks (react, angular, or vue", "field": "computer science, engineering, or related field • 5+ years of professional software development experience • strong proficiency in java script, type script, and modern frameworks (react, angular, or vue", "priority": "required"}, {"requirement": "master's degree in computer science or related field • experience with microservices architecture • knowledge of machine learning and data science concepts • familiarity with mobile development (react native, flutter) • experience with testing frameworks (jest, cypress, selenium) • aws or azure certifications • experience with agile development methodologies • leadership experience or mentoring background technical skills required: • programming languages: java script, type script, python, java • frontend: react, html5, css3, sass/less, webpack • backend: node", "field": "computer science or related field • experience with microservices architecture • knowledge of machine learning and data science concepts • familiarity with mobile development (react native, flutter) • experience with testing frameworks (jest, cypress, selenium) • aws or azure certifications • experience with agile development methodologies • leadership experience or mentoring background technical skills required: • programming languages: java script, type script, python, java • frontend: react, html5, css3, sass/less, webpack • backend: node", "priority": "required"}, {"requirement": "mance applications", "field": "nce applications", "priority": "preferred"}, {"requirement": "ms to define, design, and ship new features • write clean, maintainable, and efficient code following best practices • participate in code reviews and provide constructive feedback to team members • troubleshoot and debug applications to optimize performance • mentor junior developers and contribute to team knowledge sharing required qualifications: • bachelor's degree in computer science, engineering, or related field • 5+ years of professional software development experience • strong proficiency in java script, type script, and modern frameworks (react, angular, or vue", "field": "to define, design, and ship new features • write clean, maintainable, and efficient code following best practices • participate in code reviews and provide constructive feedback to team members • troubleshoot and debug applications to optimize performance • mentor junior developers and contribute to team knowledge sharing required qualifications: • bachelor's degree in computer science, engineering, or related field • 5+ years of professional software development experience • strong proficiency in java script, type script, and modern frameworks (react, angular, or vue", "priority": "required"}, {"requirement": "backend technologies (node", "field": "ckend technologies (node", "priority": "preferred"}, {"requirement": "bases (postgre sql, my sql) and no sql databases (mongo db) • proficiency with version control systems (git) • experience with cloud platforms (aws, azure, or google cloud platform) • knowledge of containerization technologies (docker, kuberne<PERSON>) • understanding of ci/cd pipelines and dev ops practices • strong problem-solving skills and attention to detail • excellent communication and teamwork abilities preferred qualifications: • master's degree in computer science or related field • experience with microservices architecture • knowledge of machine learning and data science concepts • familiarity with mobile development (react native, flutter) • experience with testing frameworks (jest, cypress, selenium) • aws or azure certifications • experience with agile development methodologies • leadership experience or mentoring background technical skills required: • programming languages: java script, type script, python, java • frontend: react, html5, css3, sass/less, webpack • backend: node", "field": "ses (postgre sql, my sql) and no sql databases (mongo db) • proficiency with version control systems (git) • experience with cloud platforms (aws, azure, or google cloud platform) • knowledge of containerization technologies (docker, kubernetes) • understanding of ci/cd pipelines and dev ops practices • strong problem-solving skills and attention to detail • excellent communication and teamwork abilities preferred qualifications: • master's degree in computer science or related field • experience with microservices architecture • knowledge of machine learning and data science concepts • familiarity with mobile development (react native, flutter) • experience with testing frameworks (jest, cypress, selenium) • aws or azure certifications • experience with agile development methodologies • leadership experience or mentoring background technical skills required: • programming languages: java script, type script, python, java • frontend: react, html5, css3, sass/less, webpack • backend: node", "priority": "required"}, {"requirement": "bases: postgre sql, mongo db, redis • cloud: aws (ec2, s3, lambda, rds), docker, kubernetes • tools: git, jenkins, jira, slack nice to have: • experience with react native for mobile development • knowledge of blockchain technologies • familiarity with ai/ml frameworks (tensor flow, py torch) • experience with data visualization tools (d3", "field": "ses: postgre sql, mongo db, redis • cloud: aws (ec2, s3, lambda, rds), docker, kubernetes • tools: git, jenkins, jira, slack nice to have: • experience with react native for mobile development • knowledge of blockchain technologies • familiarity with ai/ml frameworks (tensor flow, py torch) • experience with data visualization tools (d3", "priority": "preferred"}, {"requirement": "matching • flexible work hours and remote work options • professional development budget ($2, 000 annually) • unlimited pto policy • modern office with free snacks and beverages how to apply: please submit your resume, cover letter, and portfolio showcasing your best work", "field": "tching • flexible work hours and remote work options • professional development budget ($2, 000 annually) • unlimited pto policy • modern office with free snacks and beverages how to apply: please submit your resume, cover letter, and portfolio showcasing your best work", "priority": "preferred"}, {"requirement": "degree in computer science, engineering, or related field • 5+ years of professional software development experience • strong proficiency in java script, type script, and modern frameworks (react, angular, or vue", "field": "computer science, engineering, or related field • 5+ years of professional software development experience • strong proficiency in java script, type script, and modern frameworks (react, angular, or vue", "priority": "required"}, {"requirement": "degree in computer science or related field • experience with microservices architecture • knowledge of machine learning and data science concepts • familiarity with mobile development (react native, flutter) • experience with testing frameworks (jest, cypress, selenium) • aws or azure certifications • experience with agile development methodologies • leadership experience or mentoring background technical skills required: • programming languages: java script, type script, python, java • frontend: react, html5, css3, sass/less, webpack • backend: node", "field": "computer science or related field • experience with microservices architecture • knowledge of machine learning and data science concepts • familiarity with mobile development (react native, flutter) • experience with testing frameworks (jest, cypress, selenium) • aws or azure certifications • experience with agile development methodologies • leadership experience or mentoring background technical skills required: • programming languages: java script, type script, python, java • frontend: react, html5, css3, sass/less, webpack • backend: node", "priority": "required"}], "certifications": [{"certification": "aws, azure, or google cloud platform) • knowledge of containerization technologies (docker, kuberne<PERSON>) • understanding of ci/cd pipelines and dev ops practices • strong problem-solving skills and attention to detail • excellent communication and teamwork abilities preferred qualifications: • master's degree in computer science or related field • experience with microservices architecture • knowledge of machine learning and data science concepts • familiarity with mobile development (react native, flutter) • experience with testing frameworks (jest, cypress, selenium) • aws or azure certifications • experience with agile development methodologies • leadership experience or mentoring background technical skills required: • programming languages: java script, type script, python, java • frontend: react, html5, css3, sass/less, webpack • backend: node", "priority": "required"}, {"certification": "aws (ec2, s3, lambda, rds), docker, kubernetes • tools: git, jenkins, jira, slack nice to have: • experience with react native for mobile development • knowledge of blockchain technologies • familiarity with ai/ml frameworks (tensor flow, py torch) • experience with data visualization tools (d3", "priority": "preferred"}, {"certification": "azure, or google cloud platform) • knowledge of containerization technologies (docker, kuberne<PERSON>) • understanding of ci/cd pipelines and dev ops practices • strong problem-solving skills and attention to detail • excellent communication and teamwork abilities preferred qualifications: • master's degree in computer science or related field • experience with microservices architecture • knowledge of machine learning and data science concepts • familiarity with mobile development (react native, flutter) • experience with testing frameworks (jest, cypress, selenium) • aws or azure certifications • experience with agile development methodologies • leadership experience or mentoring background technical skills required: • programming languages: java script, type script, python, java • frontend: react, html5, css3, sass/less, webpack • backend: node", "priority": "required"}, {"certification": "google cloud platform) • knowledge of containerization technologies (docker, kuberne<PERSON>) • understanding of ci/cd pipelines and dev ops practices • strong problem-solving skills and attention to detail • excellent communication and teamwork abilities preferred qualifications: • master's degree in computer science or related field • experience with microservices architecture • knowledge of machine learning and data science concepts • familiarity with mobile development (react native, flutter) • experience with testing frameworks (jest, cypress, selenium) • aws or azure certifications • experience with agile development methodologies • leadership experience or mentoring background technical skills required: • programming languages: java script, type script, python, java • frontend: react, html5, css3, sass/less, webpack • backend: node", "priority": "required"}, {"certification": "certifications • experience with agile development methodologies • leadership experience or mentoring background technical skills required: • programming languages: java script, type script, python, java • frontend: react, html5, css3, sass/less, webpack • backend: node", "priority": "required"}], "soft_skills": [{"skill": "communication", "context": "olving skills and attention to detail • Excellent communication and teamwork abilities Preferred Qualifications: "}, {"skill": "leadership", "context": "Experience with agile development methodologies • Leadership experience or mentoring background Technical Skil"}, {"skill": "teamwork", "context": "attention to detail • Excellent communication and teamwork abilities Preferred Qualifications: • Master's de"}, {"skill": "flexible", "context": "vision insurance • 401(k) with company matching • Flexible work hours and remote work options • Professional"}], "job_level": "senior", "summary": {"total_required_skills": 33, "total_preferred_skills": 18, "has_experience_requirements": false, "has_education_requirements": true, "has_certifications": true, "soft_skills_count": 4, "job_level": "senior", "top_required_skills": ["mongodb", "google cloud platform", "javascript", "python", "java"], "top_preferred_skills": ["docker", "react native", "kubernetes", "aws", "amazon web services"]}, "ai_skills": {"required": [{"skill": "java", "confidence": 0.9, "source": "mock_entity", "context": "Java"}, {"skill": "v", "confidence": 0.9, "source": "mock_entity", "context": "Java"}, {"skill": "r", "confidence": 0.9, "source": "mock_entity", "context": "React"}, {"skill": "c", "confidence": 0.9, "source": "mock_entity", "context": "React"}, {"skill": "react", "confidence": 0.9, "source": "mock_entity", "context": "React"}, {"skill": "python", "confidence": 0.9, "source": "mock_entity", "context": "Python"}, {"skill": "agile", "confidence": 0.9, "source": "pattern_match", "context": ") • AWS or Azure certifications • Experience with agile development methodologies • Leadership experience"}, {"skill": "mentoring", "confidence": 0.9, "source": "pattern_match", "context": "elopment methodologies • Leadership experience or mentoring background Technical Skills Required: • Programmi"}], "preferred": [{"skill": "javascript", "confidence": 0.9, "source": "mock_entity", "context": "Java"}, {"skill": "react native", "confidence": 0.9, "source": "mock_entity", "context": "React"}, {"skill": "sql", "confidence": 0.9, "source": "mock_entity", "context": "SQL"}, {"skill": "plsql", "confidence": 0.9, "source": "mock_entity", "context": "SQL"}, {"skill": "t-sql", "confidence": 0.9, "source": "mock_entity", "context": "SQL"}, {"skill": "nosql", "confidence": 0.9, "source": "mock_entity", "context": "SQL"}, {"skill": "mysql", "confidence": 0.9, "source": "mock_entity", "context": "SQL"}, {"skill": "postgresql", "confidence": 0.9, "source": "mock_entity", "context": "SQL"}, {"skill": "sqlite", "confidence": 0.9, "source": "mock_entity", "context": "SQL"}, {"skill": "sql server", "confidence": 0.9, "source": "mock_entity", "context": "SQL"}, {"skill": "spark sql", "confidence": 0.9, "source": "mock_entity", "context": "SQL"}, {"skill": "aws", "confidence": 0.9, "source": "mock_entity", "context": "AWS"}, {"skill": "aws lambda", "confidence": 0.9, "source": "mock_entity", "context": "AWS"}, {"skill": "aws ecs", "confidence": 0.9, "source": "mock_entity", "context": "AWS"}, {"skill": "aws fargate", "confidence": 0.9, "source": "mock_entity", "context": "AWS"}, {"skill": "aws eks", "confidence": 0.9, "source": "mock_entity", "context": "AWS"}, {"skill": "aws greengrass", "confidence": 0.9, "source": "mock_entity", "context": "AWS"}, {"skill": "docker", "confidence": 0.9, "source": "mock_entity", "context": "<PERSON>er"}, {"skill": "docker swarm", "confidence": 0.9, "source": "mock_entity", "context": "<PERSON>er"}, {"skill": "kubernetes", "confidence": 0.9, "source": "mock_entity", "context": "Kubernetes"}, {"skill": "sass", "confidence": 0.9, "source": "pattern_match", "context": "ipt, Python, Java • Frontend: React, HTML5, CSS3, SASS/LESS, Webpack • Backend: Node. js, Express. js, R"}, {"skill": "less", "confidence": 0.9, "source": "pattern_match", "context": "Python, Java • Frontend: React, HTML5, CSS3, SASS/LESS, Webpack • Backend: Node. js, Express. js, RESTfu"}, {"skill": "vue", "confidence": 0.9, "source": "pattern_match", "context": "Script, and modern frameworks (React, Angular, or Vue. js) • Solid experience with backend technologies"}, {"skill": "angular", "confidence": 0.9, "source": "pattern_match", "context": "cript, <PERSON> Script, and modern frameworks (React, Angular, or Vue. js) • Solid experience with backend tech"}, {"skill": "express", "confidence": 0.9, "source": "pattern_match", "context": "L5, CSS3, SASS/LESS, Webpack • Backend: Node. js, Express. js, RESTful APIs, Graph QL • Databases: Postgre"}, {"skill": "flutter", "confidence": 0.9, "source": "pattern_match", "context": "amiliarity with mobile development (React Native, Flutter) • Experience with testing frameworks (<PERSON><PERSON>, Cypr"}, {"skill": "redis", "confidence": 0.9, "source": "pattern_match", "context": "PIs, Graph QL • Databases: Postgre SQL, Mongo DB, Redis • Cloud: AWS (EC2, S3, Lambda, RDS), <PERSON><PERSON>, <PERSON><PERSON>"}, {"skill": "azure", "confidence": 0.9, "source": "pattern_match", "context": "ems (Git) • Experience with cloud platforms (AWS, Azure, or Google Cloud Platform) • Knowledge of contain"}, {"skill": "google cloud platform", "confidence": 0.9, "source": "pattern_match", "context": "• Experience with cloud platforms (AWS, Azure, or Google Cloud Platform) • Knowledge of containerization technologies (Do"}, {"skill": "google cloud", "confidence": 0.9, "source": "pattern_match", "context": "• Experience with cloud platforms (AWS, Azure, or Google Cloud Platform) • Knowledge of containerization technol"}, {"skill": "jenkins", "confidence": 0.9, "source": "pattern_match", "context": "3, <PERSON><PERSON>, <PERSON><PERSON>), <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> • Tools: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> to Have: • Experience with <PERSON><PERSON>"}, {"skill": "git", "confidence": 0.9, "source": "pattern_match", "context": "o DB) • Proficiency with version control systems (Git) • Experience with cloud platforms (AWS, Azure, o"}, {"skill": "jira", "confidence": 0.9, "source": "pattern_match", "context": ", R<PERSON>), <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> • Tools: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> to Have: • Experience with <PERSON><PERSON><PERSON>"}, {"skill": "slack", "confidence": 0.9, "source": "pattern_match", "context": ", <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> • Tools: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> to Have: • Experience with React Native for"}, {"skill": "webpack", "confidence": 0.9, "source": "pattern_match", "context": ", Java • Frontend: React, HTML5, CSS3, SASS/LESS, Webpack • Backend: Node. js, Express. js, RESTful APIs, G"}, {"skill": "jest", "confidence": 0.9, "source": "pattern_match", "context": "e, Flutter) • Experience with testing frameworks (Jest, Cypress, Selenium) • AWS or Azure certifications"}, {"skill": "cypress", "confidence": 0.9, "source": "pattern_match", "context": "tter) • Experience with testing frameworks (Jest, Cypress, Selenium) • AWS or Azure certifications • Experi"}, {"skill": "selenium", "confidence": 0.9, "source": "pattern_match", "context": "xperience with testing frameworks (Jest, Cypress, Selenium) • AWS or Azure certifications • Experience with"}, {"skill": "blockchain", "confidence": 0.9, "source": "pattern_match", "context": "eact Native for mobile development • Knowledge of blockchain technologies • Familiarity with AI/ML frameworks"}, {"skill": "microservices", "confidence": 0.9, "source": "pattern_match", "context": "mputer Science or related field • Experience with microservices architecture • Knowledge of machine learning and"}, {"skill": "mongodb", "confidence": 0.8, "source": "pattern_match", "context": "bases (Postgre SQL, My SQL) and No SQL databases (Mongo DB) • Proficiency with version control systems (G"}, {"skill": "code review", "confidence": 0.8, "source": "pattern_match", "context": "nt code following best practices • Participate in code reviews and provide constructive feedback to team members"}]}}, "summary": {"job_title": null, "company_name": "Tech Corp Solutions Location: San Francisco, CA (Remote options available) Employment Type: Full-time Salary: $120, 000 - $160, 000 per year About Tech Corp Solutions: Tech Corp Solutions is a leading technology company specializing in innovative software solutions for enterprise clients.", "job_level": "senior", "location": "San Francisco, CA (Remote options available) Employment Type: Full-time Salary: $120, 000 - $160, 000 per year About Tech Corp Solutions: Tech Corp Solutions is a leading technology company specializing in innovative software solutions for enterprise clients.", "employment_type": "full-time", "total_required_skills": 33, "total_preferred_skills": 18, "has_experience_requirements": false, "has_education_requirements": true, "has_certifications": true, "soft_skills_count": 4, "top_required_skills": ["mongodb", "google cloud platform", "javascript", "python", "java"], "top_preferred_skills": ["docker", "react native", "kubernetes", "aws", "amazon web services"], "ai_required_skills": ["java", "v", "r", "c", "react"], "ai_preferred_skills": ["javascript", "react native", "sql", "plsql", "t-sql"], "experience_years": null, "education_requirements": ["bachelor's degree in computer science, engineering, or related field • 5+ years of professional software development experience • strong proficiency in java script, type script, and modern frameworks (react, angular, or vue", "master's degree in computer science or related field • experience with microservices architecture • knowledge of machine learning and data science concepts • familiarity with mobile development (react native, flutter) • experience with testing frameworks (jest, cypress, selenium) • aws or azure certifications • experience with agile development methodologies • leadership experience or mentoring background technical skills required: • programming languages: java script, type script, python, java • frontend: react, html5, css3, sass/less, webpack • backend: node", "mance applications"], "certifications": ["aws, azure, or google cloud platform) • knowledge of containerization technologies (docker, kuberne<PERSON>) • understanding of ci/cd pipelines and dev ops practices • strong problem-solving skills and attention to detail • excellent communication and teamwork abilities preferred qualifications: • master's degree in computer science or related field • experience with microservices architecture • knowledge of machine learning and data science concepts • familiarity with mobile development (react native, flutter) • experience with testing frameworks (jest, cypress, selenium) • aws or azure certifications • experience with agile development methodologies • leadership experience or mentoring background technical skills required: • programming languages: java script, type script, python, java • frontend: react, html5, css3, sass/less, webpack • backend: node", "aws (ec2, s3, lambda, rds), docker, kubernetes • tools: git, jenkins, jira, slack nice to have: • experience with react native for mobile development • knowledge of blockchain technologies • familiarity with ai/ml frameworks (tensor flow, py torch) • experience with data visualization tools (d3", "azure, or google cloud platform) • knowledge of containerization technologies (docker, kuberne<PERSON>) • understanding of ci/cd pipelines and dev ops practices • strong problem-solving skills and attention to detail • excellent communication and teamwork abilities preferred qualifications: • master's degree in computer science or related field • experience with microservices architecture • knowledge of machine learning and data science concepts • familiarity with mobile development (react native, flutter) • experience with testing frameworks (jest, cypress, selenium) • aws or azure certifications • experience with agile development methodologies • leadership experience or mentoring background technical skills required: • programming languages: java script, type script, python, java • frontend: react, html5, css3, sass/less, webpack • backend: node"], "salary_range": null, "benefits_count": 4, "responsibilities_count": 5}}