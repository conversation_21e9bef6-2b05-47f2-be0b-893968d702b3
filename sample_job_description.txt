Senior Software Engineer - Full Stack Development

Company: TechCorp Solutions
Location: San Francisco, CA (Remote options available)
Employment Type: Full-time
Salary: $120,000 - $160,000 per year

About TechCorp Solutions:
TechCorp Solutions is a leading technology company specializing in innovative software solutions for enterprise clients. We are a fast-growing startup with over 200 employees, focused on delivering cutting-edge products that transform how businesses operate.

Job Description:
We are seeking a highly skilled Senior Software Engineer to join our dynamic development team. The ideal candidate will have extensive experience in full-stack development and a passion for building scalable, high-performance applications.

Key Responsibilities:
• Design and develop robust, scalable web applications using modern technologies
• Collaborate with cross-functional teams to define, design, and ship new features
• Write clean, maintainable, and efficient code following best practices
• Participate in code reviews and provide constructive feedback to team members
• Troubleshoot and debug applications to optimize performance
• Mentor junior developers and contribute to team knowledge sharing

Required Qualifications:
• Bachelor's degree in Computer Science, Engineering, or related field
• 5+ years of professional software development experience
• Strong proficiency in JavaScript, TypeScript, and modern frameworks (React, Angular, or Vue.js)
• Solid experience with backend technologies (Node.js, Python, or Java)
• Experience with relational databases (PostgreSQL, MySQL) and NoSQL databases (MongoDB)
• Proficiency with version control systems (Git)
• Experience with cloud platforms (AWS, Azure, or Google Cloud Platform)
• Knowledge of containerization technologies (Docker, Kubernetes)
• Understanding of CI/CD pipelines and DevOps practices
• Strong problem-solving skills and attention to detail
• Excellent communication and teamwork abilities

Preferred Qualifications:
• Master's degree in Computer Science or related field
• Experience with microservices architecture
• Knowledge of machine learning and data science concepts
• Familiarity with mobile development (React Native, Flutter)
• Experience with testing frameworks (Jest, Cypress, Selenium)
• AWS or Azure certifications
• Experience with agile development methodologies
• Leadership experience or mentoring background

Technical Skills Required:
• Programming Languages: JavaScript, TypeScript, Python, Java
• Frontend: React, HTML5, CSS3, SASS/LESS, Webpack
• Backend: Node.js, Express.js, RESTful APIs, GraphQL
• Databases: PostgreSQL, MongoDB, Redis
• Cloud: AWS (EC2, S3, Lambda, RDS), Docker, Kubernetes
• Tools: Git, Jenkins, JIRA, Slack

Nice to Have:
• Experience with React Native for mobile development
• Knowledge of blockchain technologies
• Familiarity with AI/ML frameworks (TensorFlow, PyTorch)
• Experience with data visualization tools (D3.js, Chart.js)

Benefits:
• Competitive salary and equity package
• Comprehensive health, dental, and vision insurance
• 401(k) with company matching
• Flexible work hours and remote work options
• Professional development budget ($2,000 annually)
• Unlimited PTO policy
• Modern office with free snacks and beverages

How to Apply:
Please submit your resume, cover letter, and portfolio showcasing your best work. We are an equal opportunity employer committed to diversity and inclusion.
